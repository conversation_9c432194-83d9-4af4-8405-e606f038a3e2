use std::sync::Arc;
use log::{info, error};
use sqlx::{MySql, Pool, Row};
use thiserror::Error;

use crate::models::mysql::{MySqlUser, MySqlFavorite, MySqlBookmark, MySqlAppVersion, MySqlTag, MySqlBookmarkTag, MySqlCollection, MySqlNote, MySqlNoteDraft, MySqlMaterial, MySqlAugmentUser, MySqlTencentAsrTask, MySqlTask, MySqlPrompt, MySqlSystemPrompt, MySqlAiUsage};

/// 数据库表结构管理错误
#[derive(Debug, Error)]
pub enum SchemaManagerError {
    #[error("数据库错误: {0}")]
    DatabaseError(#[from] sqlx::Error),

    #[error("表已存在: {0}")]
    TableAlreadyExists(String),

    #[error("表不存在: {0}")]
    TableNotExists(String),
}

/// 数据库表结构管理器
pub struct SchemaManager {
    /// MySQL连接池
    pool: Arc<Pool<MySql>>,
    /// 数据库名称
    database: String,
}

impl SchemaManager {
    /// 创建新的表结构管理器
    pub fn new(pool: Pool<MySql>, database: String) -> Self {
        Self {
            pool: Arc::new(pool),
            database,
        }
    }

    /// 初始化数据库表结构
    pub async fn initialize_schema(&self) -> Result<(), SchemaManagerError> {
        info!("开始初始化数据库表结构...");

        // 检查并创建用户表
        self.ensure_table_exists(MySqlUser::table_name(), Self::get_users_table_sql()).await?;

        // 检查并创建收藏夹表
        self.ensure_table_exists(MySqlFavorite::table_name(), Self::get_favorites_table_sql()).await?;

        // 检查并创建书签表
        self.ensure_table_exists(MySqlBookmark::table_name(), Self::get_bookmarks_table_sql()).await?;

        // 检查并创建应用版本表
        self.ensure_table_exists(MySqlAppVersion::table_name(), Self::get_app_versions_table_sql()).await?;

        // 检查并创建标签表
        self.ensure_table_exists(MySqlTag::table_name(), Self::get_tags_table_sql()).await?;

        // 检查并创建书签标签关联表
        self.ensure_table_exists(MySqlBookmarkTag::table_name(), Self::get_bookmark_tags_table_sql()).await?;

        // 检查并创建合集表
        self.ensure_table_exists(MySqlCollection::table_name(), Self::get_collections_table_sql()).await?;

        // 检查并创建笔记表
        self.ensure_table_exists(MySqlNote::table_name(), Self::get_notes_table_sql()).await?;

        // 检查并创建笔记草稿表
        self.ensure_table_exists(MySqlNoteDraft::table_name(), Self::get_note_drafts_table_sql()).await?;

        // 检查并创建素材表
        self.ensure_table_exists(MySqlMaterial::table_name(), Self::get_materials_table_sql()).await?;

        // 检查并创建Augment用户表
        self.ensure_table_exists(MySqlAugmentUser::table_name(), Self::get_augment_users_table_sql()).await?;

        // 检查并创建腾讯云语音识别任务表
        self.ensure_table_exists(MySqlTencentAsrTask::table_name(), Self::get_tencent_asr_tasks_table_sql()).await?;

        // 检查并创建任务表
        self.ensure_table_exists(MySqlTask::table_name(), Self::get_tasks_table_sql()).await?;

        // 检查并创建提示词表
        self.ensure_table_exists(MySqlPrompt::table_name(), Self::get_prompts_table_sql()).await?;

        // 检查并创建系统提示词表
        self.ensure_table_exists(MySqlSystemPrompt::table_name(), Self::get_system_prompts_table_sql()).await?;

        // 检查并创建AI使用次数表
        self.ensure_table_exists(MySqlAiUsage::table_name(), Self::get_ai_usage_table_sql()).await?;

        info!("数据库表结构初始化完成");
        Ok(())
    }

    /// 确保表存在，如果不存在则创建
    async fn ensure_table_exists(&self, table_name: &str, create_sql: &str) -> Result<(), SchemaManagerError> {
        // 检查表是否存在
        if self.check_table_exists(table_name).await? {
            info!("表 {} 已存在，跳过创建", table_name);
            return Ok(());
        }

        // 创建表
        info!("表 {} 不存在，开始创建...", table_name);
        sqlx::query(create_sql)
            .execute(&*self.pool)
            .await?;

        info!("表 {} 创建成功", table_name);
        Ok(())
    }

    /// 检查表是否存在
    async fn check_table_exists(&self, table_name: &str) -> Result<bool, SchemaManagerError> {
        let result = sqlx::query(
            "SELECT COUNT(*) as count FROM information_schema.tables
             WHERE table_schema = ? AND table_name = ?"
        )
        .bind(&self.database)
        .bind(table_name)
        .fetch_one(&*self.pool)
        .await?;

        let count: i64 = result.get("count");
        Ok(count > 0)
    }



    /// 获取用户表创建SQL
    fn get_users_table_sql() -> &'static str {
        r#"
        CREATE TABLE users (
            id BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
            phone VARCHAR(20) NOT NULL,
            password VARCHAR(255) NOT NULL,
            created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            mongo_id VARCHAR(24),
            PRIMARY KEY (id),
            UNIQUE KEY idx_phone (phone)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        "#
    }

    /// 获取收藏夹表创建SQL
    fn get_favorites_table_sql() -> &'static str {
        r#"
        CREATE TABLE favorites (
            id BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
            user_id BIGINT UNSIGNED NOT NULL,
            name VARCHAR(50) NOT NULL,
            cover TEXT NOT NULL,
            `order` INT NOT NULL DEFAULT 0,
            created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            mongo_id VARCHAR(24),
            PRIMARY KEY (id),
            KEY idx_user_id (user_id),
            KEY idx_order (user_id, `order`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        "#
    }

    /// 获取书签表创建SQL
    fn get_bookmarks_table_sql() -> &'static str {
        r#"
        CREATE TABLE bookmarks (
            id BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
            user_id BIGINT UNSIGNED NOT NULL,
            favorite_id BIGINT UNSIGNED NOT NULL,
            influencer_name VARCHAR(100) NOT NULL,
            influencer_avatar TEXT,
            cover TEXT NOT NULL,
            title VARCHAR(255) NOT NULL,
            `desc` TEXT NOT NULL,
            scheme_url TEXT NOT NULL,
            created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            mongo_id VARCHAR(24),
            PRIMARY KEY (id),
            KEY idx_user_id (user_id),
            KEY idx_favorite_id (favorite_id),
            KEY idx_user_favorite (user_id, favorite_id)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        "#
    }

    /// 获取应用版本表创建SQL
    fn get_app_versions_table_sql() -> &'static str {
        r#"
        CREATE TABLE app_versions (
            id BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
            version VARCHAR(50) NOT NULL,
            update_description JSON NOT NULL,
            is_current BOOLEAN NOT NULL DEFAULT FALSE,
            created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            UNIQUE KEY idx_version (version),
            KEY idx_is_current (is_current)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        "#
    }

    /// 获取标签表创建SQL
    fn get_tags_table_sql() -> &'static str {
        r#"
        CREATE TABLE tags (
            id BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
            name VARCHAR(50) NOT NULL,
            background_color VARCHAR(7) NOT NULL DEFAULT '#1890ff',
            text_color VARCHAR(7) NOT NULL DEFAULT '#ffffff',
            user_id BIGINT UNSIGNED NOT NULL,
            created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            UNIQUE KEY idx_user_tag (user_id, name),
            KEY idx_user_id (user_id),
            KEY idx_name (name)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        "#
    }

    /// 获取书签标签关联表创建SQL
    fn get_bookmark_tags_table_sql() -> &'static str {
        r#"
        CREATE TABLE bookmark_tags (
            id BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
            bookmark_id BIGINT UNSIGNED NOT NULL,
            tag_id BIGINT UNSIGNED NOT NULL,
            created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            UNIQUE KEY idx_bookmark_tag (bookmark_id, tag_id),
            KEY idx_bookmark_id (bookmark_id),
            KEY idx_tag_id (tag_id),
            FOREIGN KEY (bookmark_id) REFERENCES bookmarks(id) ON DELETE CASCADE,
            FOREIGN KEY (tag_id) REFERENCES tags(id) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        "#
    }

    /// 获取合集表创建SQL
    fn get_collections_table_sql() -> &'static str {
        r#"
        CREATE TABLE collections (
            id BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
            user_id BIGINT UNSIGNED NOT NULL,
            name VARCHAR(100) NOT NULL,
            cover TEXT NOT NULL,
            `desc` TEXT NOT NULL,
            create_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
            update_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY idx_user_id (user_id),
            FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        "#
    }

    /// 获取笔记表创建SQL
    fn get_notes_table_sql() -> &'static str {
        r#"
        CREATE TABLE notes (
            id BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
            parent_id BIGINT UNSIGNED NULL,
            user_id BIGINT UNSIGNED NOT NULL,
            title VARCHAR(255) NOT NULL,
            cover TEXT NOT NULL,
            `desc` TEXT NOT NULL,
            content LONGTEXT NULL,
            html LONGTEXT NOT NULL,
            status TINYINT NULL DEFAULT 1 COMMENT '笔记状态：1正常 2已删除',
            create_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
            update_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY idx_parent_id (parent_id),
            KEY idx_user_id (user_id),
            KEY idx_user_parent (user_id, parent_id),
            KEY idx_user_status (user_id, status),
            FOREIGN KEY (parent_id) REFERENCES collections(id) ON DELETE CASCADE,
            FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        "#
    }

    /// 获取素材表创建SQL
    fn get_materials_table_sql() -> &'static str {
        r#"
        CREATE TABLE materials (
            id BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
            user_id BIGINT UNSIGNED NOT NULL,
            url TEXT NOT NULL,
            create_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
            update_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            status TINYINT NOT NULL DEFAULT 1 COMMENT '1正常 2已删除',
            type TINYINT NOT NULL COMMENT '1图片 2语音 3视频',
            PRIMARY KEY (id),
            KEY idx_user_id (user_id),
            KEY idx_user_status (user_id, status),
            KEY idx_user_type (user_id, type),
            FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        "#
    }

    /// 获取Augment用户表创建SQL
    fn get_augment_users_table_sql() -> &'static str {
        r#"
        CREATE TABLE augment_users (
            id BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
            account VARCHAR(100) NOT NULL,
            password VARCHAR(255) NOT NULL,
            is_member BOOLEAN NOT NULL DEFAULT FALSE,
            member_expire_time TIMESTAMP NULL,
            created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            UNIQUE KEY idx_account (account),
            KEY idx_is_member (is_member),
            KEY idx_member_expire (member_expire_time)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        "#
    }

    /// 获取腾讯云语音识别任务表创建SQL
    fn get_tencent_asr_tasks_table_sql() -> &'static str {
        r#"
        CREATE TABLE tencent_asr_tasks (
            id BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
            task_id BIGINT UNSIGNED NOT NULL,
            request_id VARCHAR(255) NOT NULL,
            related_task_id BIGINT UNSIGNED NULL COMMENT '关联的任务表ID（外键，可选）',
            status TINYINT NOT NULL DEFAULT 1 COMMENT '1识别中 2识别成功 3识别失败',
            result TEXT NULL,
            created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            UNIQUE KEY idx_task_id (task_id),
            KEY idx_request_id (request_id),
            KEY idx_related_task_id (related_task_id),
            KEY idx_status (status),
            KEY idx_created_at (created_at),
            FOREIGN KEY (related_task_id) REFERENCES tasks(id) ON DELETE SET NULL
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        "#
    }

    /// 获取任务表创建SQL
    fn get_tasks_table_sql() -> &'static str {
        r#"
        CREATE TABLE tasks (
            id BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
            user_id BIGINT UNSIGNED NOT NULL COMMENT '用户ID（外键）',
            task_type TINYINT NOT NULL COMMENT '1提取文案 2下载视频 3创建笔记 4更新笔记',
            title VARCHAR(255) NOT NULL,
            platform VARCHAR(50) NOT NULL,
            url TEXT NOT NULL COMMENT '提取的地址URL',
            status TINYINT NOT NULL DEFAULT 1 COMMENT '1新创建 2已完成 3失败',
            result TEXT NULL COMMENT '任务结果',
            note_id BIGINT UNSIGNED NULL COMMENT '笔记ID（外键，可选，当task_type为4时存在）',
            create_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
            update_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY idx_user_id (user_id),
            KEY idx_user_task_type (user_id, task_type),
            KEY idx_user_status (user_id, status),
            KEY idx_user_platform (user_id, platform),
            KEY idx_create_time (create_time),
            KEY idx_note_id (note_id),
            FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
            FOREIGN KEY (note_id) REFERENCES notes(id) ON DELETE SET NULL
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        "#
    }

    /// 获取提示词表创建SQL
    fn get_prompts_table_sql() -> &'static str {
        r#"
        CREATE TABLE prompts (
            id BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
            user_id BIGINT UNSIGNED NOT NULL COMMENT '用户ID（外键）',
            title VARCHAR(255) NOT NULL COMMENT '提示词标题',
            content TEXT NOT NULL COMMENT '提示词内容',
            category VARCHAR(100) NULL COMMENT '提示词分类',
            tags VARCHAR(500) NULL COMMENT '提示词标签，多个标签用逗号分隔',
            is_public TINYINT NOT NULL DEFAULT 0 COMMENT '是否公开：0私有 1公开',
            usage_count BIGINT UNSIGNED NOT NULL DEFAULT 0 COMMENT '使用次数',
            created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY idx_user_id (user_id),
            KEY idx_user_category (user_id, category),
            KEY idx_user_public (user_id, is_public),
            KEY idx_public (is_public),
            KEY idx_category (category),
            KEY idx_created_at (created_at),
            FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        "#
    }

    /// 获取系统提示词表创建SQL
    fn get_system_prompts_table_sql() -> &'static str {
        r#"
        CREATE TABLE system_prompts (
            id BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
            title VARCHAR(255) NULL COMMENT '系统提示词标题',
            content TEXT NULL COMMENT '系统提示词内容',
            category VARCHAR(100) NULL COMMENT '系统提示词分类',
            tags VARCHAR(500) NULL COMMENT '系统提示词标签，多个标签用逗号分隔',
            is_enabled TINYINT NOT NULL DEFAULT 1 COMMENT '是否启用：0禁用 1启用',
            sort_weight INT NOT NULL DEFAULT 0 COMMENT '排序权重，数值越大排序越靠前',
            usage_count BIGINT UNSIGNED NOT NULL DEFAULT 0 COMMENT '使用次数',
            created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY idx_enabled (is_enabled),
            KEY idx_category (category),
            KEY idx_sort_weight (sort_weight),
            KEY idx_created_at (created_at)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        "#
    }

    /// 获取笔记草稿表创建SQL
    fn get_note_drafts_table_sql() -> &'static str {
        r#"
        CREATE TABLE note_drafts (
            id BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
            note_id BIGINT UNSIGNED NOT NULL COMMENT '笔记ID（外键）',
            user_id BIGINT UNSIGNED NOT NULL COMMENT '用户ID（外键）',
            content LONGTEXT NULL COMMENT '草稿内容',
            create_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
            update_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            UNIQUE KEY idx_note_id (note_id),
            KEY idx_user_id (user_id),
            KEY idx_user_note (user_id, note_id),
            FOREIGN KEY (note_id) REFERENCES notes(id) ON DELETE CASCADE,
            FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        "#
    }

    /// 获取AI使用次数表创建SQL
    fn get_ai_usage_table_sql() -> &'static str {
        r#"
        CREATE TABLE ai_usage (
            id BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
            user_id BIGINT UNSIGNED NOT NULL COMMENT '用户ID（外键）',
            usage_count INT NULL COMMENT '使用次数',
            created_at TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            UNIQUE KEY idx_user_id (user_id),
            FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        "#
    }
}
