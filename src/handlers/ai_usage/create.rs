use actix_web::{web, HttpRequest, HttpResponse, Responder};
use log::{info, error};
use serde_json::Value;

use crate::dto::{CreateAiUsageRequest, CreateAiUsageResponse, ApiResponse, ErrorCode};
use crate::middleware::token_parser::get_user_id_from_request;
use crate::AppState;

/**
 * 创建AI使用记录处理函数
 *
 * 此函数处理POST /ai-usage/create请求，用于创建新的AI使用记录并设置次数为20
 *
 * 请求参数：
 * - user_id: 用户ID（字符串）
 *
 * 返回数据：
 * {
 *   "code": 0,
 *   "message": "AI使用记录创建成功",
 *   "data": {
 *     "id": "记录ID",
 *     "usage_count": 20
 *   }
 * }
 *
 * @param req HTTP请求
 * @param payload 请求体，包含用户ID
 * @param app_state 应用状态
 * @return HTTP响应
 */
pub async fn handle(
    req: HttpRequest,
    payload: web::Json<CreateAiUsageRequest>,
    app_state: web::Data<AppState>,
) -> impl Responder {
    // 获取当前用户ID
    let current_user_id_str = match get_user_id_from_request(&req) {
        Some(id) => id,
        None => {
            error!("未登录或身份验证失败");
            return HttpResponse::Unauthorized().json(ApiResponse {
                code: ErrorCode::TokenInvalid.code(),
                message: "未登录或身份验证失败".to_string(),
                data: Value::Null,
            });
        }
    };

    // 解析用户ID
    let user_id = match payload.user_id.parse::<u64>() {
        Ok(id) => id,
        Err(_) => {
            error!("用户ID格式无效: {}", payload.user_id);
            return HttpResponse::BadRequest().json(ApiResponse {
                code: ErrorCode::InvalidParameter.code(),
                message: "用户ID格式无效".to_string(),
                data: Value::Null,
            });
        }
    };

    info!("创建AI使用记录，用户ID: {}", user_id);

    // 获取MySQL AI使用次数服务
    let mysql_ai_usage_service = match &app_state.mysql_ai_usage_service {
        Some(service) => service,
        None => {
            error!("MySQL AI使用次数服务不可用");
            return HttpResponse::ServiceUnavailable().json(ApiResponse {
                code: ErrorCode::ServiceUnavailable.code(),
                message: "服务暂时不可用".to_string(),
                data: Value::Null,
            });
        }
    };

    // 创建AI使用记录
    match mysql_ai_usage_service.create_usage_record(user_id).await {
        Ok(record) => {
            let record_id_str = record.id.to_string();
            let usage_count = record.usage_count.unwrap_or(0);
            info!("AI使用记录创建成功，记录ID: {}, 使用次数: {}", record_id_str, usage_count);

            HttpResponse::Ok().json(ApiResponse {
                code: ErrorCode::Success.code(),
                message: "AI使用记录创建成功".to_string(),
                data: CreateAiUsageResponse {
                    id: record_id_str,
                    usage_count,
                },
            })
        }
        Err(e) => {
            error!("创建AI使用记录失败: {:?}", e);
            let (code, message) = match e {
                crate::services::mysql::MySqlAiUsageServiceError::InvalidParameter => {
                    (ErrorCode::InvalidParameter.code(), "用户已存在AI使用记录".to_string())
                }
                _ => (ErrorCode::DatabaseError.code(), "数据库错误".to_string()),
            };

            HttpResponse::InternalServerError().json(ApiResponse {
                code,
                message,
                data: Value::Null,
            })
        }
    }
}
