use actix_web::{web, HttpRequest, HttpResponse, Responder};
use log::{error, info};
use serde_json::Value;

use crate::{
    AppState,
    dto::{ApiResponse, error_code::ErrorCode, UpdateNoteRequest, UpdateNoteResponse},
    middleware::token_parser::get_user_id_from_request,
    services::mysql::MySqlNoteServiceError,
};

/**
 * 更新笔记处理函数
 *
 * 此函数处理POST /note/update请求，用于更新笔记的内容
 *
 * @param req HTTP请求
 * @param payload 请求体，包含笔记更新信息
 * @param app_state 应用状态
 * @return HTTP响应
 */
pub async fn handle(
    req: HttpRequest,
    payload: web::Json<UpdateNoteRequest>,
    app_state: web::Data<AppState>,
) -> impl Responder {
    // 获取当前用户ID
    let user_id_str = match get_user_id_from_request(&req) {
        Some(id) => id,
        None => {
            error!("未登录或身份验证失败");
            return HttpResponse::Unauthorized().json(ApiResponse {
                code: ErrorCode::TokenInvalid.code(),
                message: "未登录或身份验证失败".to_string(),
                data: Value::Null,
            });
        }
    };

    // 解析用户ID
    let user_id = match user_id_str.parse::<u64>() {
        Ok(id) => id,
        Err(_) => {
            error!("用户ID格式错误: {}", user_id_str);
            return HttpResponse::BadRequest().json(ApiResponse {
                code: ErrorCode::InvalidParameter.code(),
                message: "用户ID格式错误".to_string(),
                data: Value::Null,
            });
        }
    };

    // 解析笔记ID
    let note_id = match payload.id.parse::<u64>() {
        Ok(id) => id,
        Err(_) => {
            error!("笔记ID格式错误: {}", payload.id);
            return HttpResponse::BadRequest().json(ApiResponse {
                code: ErrorCode::InvalidParameter.code(),
                message: "笔记ID格式错误".to_string(),
                data: Value::Null,
            });
        }
    };

    // 打印接收到的数据用于调试
    info!("接收到的更新请求 - 笔记ID: {}", payload.id);
    if let Some(ref title) = payload.title {
        info!("标题: {}", title);
    }
    if let Some(ref cover) = payload.cover {
        info!("封面: {}", cover);
    }
    if let Some(ref desc) = payload.desc {
        info!("描述: {}", desc);
    }
    if let Some(ref content) = payload.content {
        info!("内容长度: {}", content.len());
    }
    // 处理HTML内容的双重编码问题
    let processed_html = if let Some(ref html) = payload.html {
        info!("原始HTML内容长度: {}", html.len());

        // 检查是否是被双重编码的JSON字符串
        let decoded_html = if html.starts_with("\"") && html.ends_with("\"") {
            // 尝试解析JSON字符串
            match serde_json::from_str::<String>(html) {
                Ok(decoded) => {
                    info!("检测到双重编码，已解码。解码后长度: {}", decoded.len());
                    decoded
                }
                Err(_) => {
                    info!("JSON解码失败，使用原始内容");
                    html.clone()
                }
            }
        } else {
            info!("未检测到双重编码，使用原始内容");
            html.clone()
        };

        info!("处理后HTML内容前100个字符: {}", &decoded_html[..decoded_html.len().min(100)]);
        Some(decoded_html)
    } else {
        None
    };

    // 验证至少有一个字段需要更新
    if payload.title.is_none()
        && payload.cover.is_none()
        && payload.desc.is_none()
        && payload.content.is_none()
        && processed_html.is_none() {
        error!("没有提供任何需要更新的字段");
        return HttpResponse::BadRequest().json(ApiResponse {
            code: ErrorCode::InvalidParameter.code(),
            message: "至少需要提供一个要更新的字段".to_string(),
            data: Value::Null,
        });
    }

    // 获取MySQL AI使用次数服务
    let mysql_ai_usage_service = match &app_state.mysql_ai_usage_service {
        Some(service) => service,
        None => {
            error!("MySQL AI使用次数服务不可用");
            return HttpResponse::ServiceUnavailable().json(ApiResponse {
                code: ErrorCode::ServiceUnavailable.code(),
                message: "服务暂时不可用".to_string(),
                data: Value::Null,
            });
        }
    };

    // 检查用户是否有权限使用AI功能
    if let Err(e) = mysql_ai_usage_service.check_permission(user_id).await {
        error!("用户AI权限检查失败: {:?}", e);
        let message = match e {
            crate::services::mysql::MySqlAiUsageServiceError::NoPermission => "无权使用AI，联系管理员".to_string(),
            crate::services::mysql::MySqlAiUsageServiceError::InsufficientUsage => "会员到期".to_string(),
            _ => "权限检查失败".to_string(),
        };
        return HttpResponse::Forbidden().json(ApiResponse {
            code: ErrorCode::PermissionDenied.code(),
            message,
            data: Value::Null,
        });
    }

    // 获取MySQL笔记服务
    let mysql_note_service = match &app_state.mysql_note_service {
        Some(service) => service,
        None => {
            error!("MySQL笔记服务不可用");
            return HttpResponse::ServiceUnavailable().json(ApiResponse {
                code: ErrorCode::ServiceUnavailable.code(),
                message: "服务暂时不可用".to_string(),
                data: Value::Null,
            });
        }
    };

    // 更新笔记
    match mysql_note_service.update_note(
        note_id,
        payload.title.clone(),
        payload.cover.clone(),
        payload.desc.clone(),
        payload.content.clone(),
        processed_html,
    ).await {
        Ok(note) => {
            let note_id_str = note.id.to_string();
            info!("笔记更新成功，笔记ID: {}", note_id_str);

            // 扣减用户使用次数
            if let Err(e) = mysql_ai_usage_service.decrease_usage(user_id).await {
                error!("扣减用户使用次数失败: {:?}", e);
                // 这里不影响笔记更新的成功响应，只记录日志
            } else {
                info!("用户使用次数扣减成功，用户ID: {}", user_id);
            }

            HttpResponse::Ok().json(ApiResponse {
                code: ErrorCode::Success.code(),
                message: "笔记更新成功".to_string(),
                data: UpdateNoteResponse {
                    id: note_id_str,
                },
            })
        }
        Err(e) => {
            error!("更新笔记失败: {}", e);

            let (error_code, message) = match e {
                MySqlNoteServiceError::NoteNotFound => {
                    (ErrorCode::InvalidParameter, "笔记不存在")
                }
                MySqlNoteServiceError::PermissionDenied => {
                    (ErrorCode::InvalidParameter, "无权限访问该笔记")
                }
                MySqlNoteServiceError::DatabaseError(_) => {
                    (ErrorCode::DatabaseError, "服务错误，请稍后再试")
                }
                MySqlNoteServiceError::DatabaseNotAvailable => {
                    (ErrorCode::ServiceUnavailable, "服务暂时不可用")
                }
                MySqlNoteServiceError::InvalidParameter => {
                    (ErrorCode::InvalidParameter, "参数无效")
                }
            };

            HttpResponse::InternalServerError().json(ApiResponse {
                code: error_code.code(),
                message: message.to_string(),
                data: Value::Null,
            })
        }
    }
}
