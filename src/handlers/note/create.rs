use actix_web::{web, HttpRequest, HttpResponse, Responder};
use log::{error, info};
use serde_json::Value;

use crate::{
    AppState,
    dto::{ApiResponse, error_code::ErrorCode, CreateNoteRequest, CreateNoteResponse},
    middleware::token_parser::get_user_id_from_request,
    services::mysql::MySqlNoteServiceError,
};

/**
 * 创建笔记处理函数
 *
 * 此函数处理POST /note/create请求，用于创建新的笔记
 *
 * @param req HTTP请求
 * @param payload 请求体，包含笔记创建信息
 * @param app_state 应用状态
 * @return HTTP响应
 */
pub async fn handle(
    req: HttpRequest,
    payload: web::Json<CreateNoteRequest>,
    app_state: web::Data<AppState>,
) -> impl Responder {
    // 获取当前用户ID
    let user_id_str = match get_user_id_from_request(&req) {
        Some(id) => id,
        None => {
            error!("未登录或身份验证失败");
            return HttpResponse::Unauthorized().json(ApiResponse {
                code: ErrorCode::TokenInvalid.code(),
                message: "未登录或身份验证失败".to_string(),
                data: Value::Null,
            });
        }
    };

    // 将字符串用户ID转换为 u64
    let user_id = match user_id_str.parse::<u64>() {
        Ok(id) => id,
        Err(e) => {
            error!("无效的用户ID格式: {}", e);
            return HttpResponse::BadRequest().json(ApiResponse {
                code: ErrorCode::InvalidParameter.code(),
                message: "无效的用户ID格式".to_string(),
                data: Value::Null,
            });
        }
    };

    // 验证参数
    if payload.title.trim().is_empty() {
        error!("笔记标题不能为空");
        return HttpResponse::BadRequest().json(ApiResponse {
            code: ErrorCode::InvalidParameter.code(),
            message: "笔记标题不能为空".to_string(),
            data: Value::Null,
        });
    }

    if payload.title.len() > 255 {
        error!("笔记标题过长");
        return HttpResponse::BadRequest().json(ApiResponse {
            code: ErrorCode::InvalidParameter.code(),
            message: "笔记标题不能超过255个字符".to_string(),
            data: Value::Null,
        });
    }

    // 解析合集ID（如果提供）
    let parent_id = if let Some(ref parent_id_str) = payload.parent_id {
        if parent_id_str.trim().is_empty() {
            None
        } else {
            match parent_id_str.parse::<u64>() {
                Ok(id) => Some(id),
                Err(_) => {
                    error!("无效的合集ID格式: {}", parent_id_str);
                    return HttpResponse::BadRequest().json(ApiResponse {
                        code: ErrorCode::InvalidParameter.code(),
                        message: "无效的合集ID格式".to_string(),
                        data: Value::Null,
                    });
                }
            }
        }
    } else {
        None
    };

    // 获取MySQL AI使用次数服务
    let mysql_ai_usage_service = match &app_state.mysql_ai_usage_service {
        Some(service) => service,
        None => {
            error!("MySQL AI使用次数服务不可用");
            return HttpResponse::ServiceUnavailable().json(ApiResponse {
                code: ErrorCode::ServiceUnavailable.code(),
                message: "服务暂时不可用".to_string(),
                data: Value::Null,
            });
        }
    };

    // 检查用户是否有权限使用AI功能
    if let Err(e) = mysql_ai_usage_service.check_permission(user_id).await {
        error!("用户AI权限检查失败: {:?}", e);
        let message = match e {
            crate::services::mysql::MySqlAiUsageServiceError::NoPermission => "无权使用AI，联系管理员".to_string(),
            crate::services::mysql::MySqlAiUsageServiceError::InsufficientUsage => "会员到期".to_string(),
            _ => "权限检查失败".to_string(),
        };
        return HttpResponse::Forbidden().json(ApiResponse {
            code: ErrorCode::PermissionDenied.code(),
            message,
            data: Value::Null,
        });
    }

    // 获取MySQL笔记服务
    let mysql_note_service = match &app_state.mysql_note_service {
        Some(service) => service,
        None => {
            error!("MySQL笔记服务不可用");
            return HttpResponse::ServiceUnavailable().json(ApiResponse {
                code: ErrorCode::ServiceUnavailable.code(),
                message: "服务暂时不可用".to_string(),
                data: Value::Null,
            });
        }
    };

    // 创建笔记
    match mysql_note_service.create_note(
        parent_id,
        user_id,
        payload.title.clone(),
        payload.cover.clone(),
        payload.desc.clone(),
        payload.content.clone(),
        payload.html.clone(),
    ).await {
        Ok(note) => {
            let note_id_str = note.id.to_string();
            info!("笔记创建成功，笔记ID: {}", note_id_str);

            // 扣减用户使用次数
            if let Err(e) = mysql_ai_usage_service.decrease_usage(user_id).await {
                error!("扣减用户使用次数失败: {:?}", e);
                // 这里不影响笔记创建的成功响应，只记录日志
            } else {
                info!("用户使用次数扣减成功，用户ID: {}", user_id);
            }

            HttpResponse::Ok().json(ApiResponse {
                code: ErrorCode::Success.code(),
                message: "笔记创建成功".to_string(),
                data: CreateNoteResponse {
                    id: note_id_str,
                },
            })
        }
        Err(e) => {
            error!("创建笔记失败: {}", e);

            let (error_code, message) = match e {
                MySqlNoteServiceError::InvalidParameter => {
                    (ErrorCode::InvalidParameter, "参数无效")
                }
                MySqlNoteServiceError::DatabaseError(_) => {
                    (ErrorCode::DatabaseError, "数据库错误")
                }
                MySqlNoteServiceError::DatabaseNotAvailable => {
                    (ErrorCode::ServiceUnavailable, "服务暂时不可用")
                }
                MySqlNoteServiceError::NoteNotFound => {
                    (ErrorCode::InvalidParameter, "笔记不存在")
                }
                MySqlNoteServiceError::PermissionDenied => {
                    (ErrorCode::InvalidParameter, "权限不足")
                }
            };

            HttpResponse::BadRequest().json(ApiResponse {
                code: error_code.code(),
                message: message.to_string(),
                data: Value::Null,
            })
        }
    }
}
