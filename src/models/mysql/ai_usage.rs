use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use sqlx::FromRow;

/// MySQL AI使用次数模型
#[derive(Debug, Serialize, Deserialize, Clone, FromRow)]
pub struct MySqlAiUsage {
    /// 记录ID
    pub id: u64,
    
    /// 用户ID（外键）
    pub user_id: u64,
    
    /// 使用次数
    pub usage_count: Option<i32>,
    
    /// 创建时间
    pub created_at: Option<DateTime<Utc>>,
    
    /// 更新时间
    pub updated_at: Option<DateTime<Utc>>,
}

impl MySqlAiUsage {
    /// 创建新的AI使用记录
    pub fn new(user_id: u64, usage_count: i32) -> Self {
        let now = Utc::now();
        Self {
            id: 0, // 数据库会自动分配ID
            user_id,
            usage_count: Some(usage_count),
            created_at: Some(now),
            updated_at: Some(now),
        }
    }

    /// 获取表名
    pub fn table_name() -> &'static str {
        "ai_usage"
    }
}
