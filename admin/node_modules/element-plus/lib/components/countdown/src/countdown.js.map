{"version": 3, "file": "countdown.js", "sources": ["../../../../../../packages/components/countdown/src/countdown.ts"], "sourcesContent": ["import { buildProps, definePropType, isNumber } from '@element-plus/utils'\nimport { CHANGE_EVENT } from '@element-plus/constants'\n\nimport type { ExtractPropTypes, StyleValue } from 'vue'\nimport type { Dayjs } from 'dayjs'\nimport type Countdown from './countdown.vue'\n\nexport const countdownProps = buildProps({\n  /**\n   * @description Formatting the countdown display\n   */\n  format: {\n    type: String,\n    default: 'HH:mm:ss',\n  },\n  /**\n   * @description Sets the prefix of a countdown\n   */\n  prefix: String,\n  /**\n   * @description Sets the suffix of a countdown\n   */\n  suffix: String,\n  /**\n   * @description countdown titles\n   */\n  title: String,\n  /**\n   * @description target time\n   */\n  value: {\n    type: definePropType<number | Dayjs>([Number, Object]),\n    default: 0,\n  },\n  /**\n   * @description Styles countdown values\n   */\n  valueStyle: {\n    type: definePropType<StyleValue>([String, Object, Array]),\n  },\n} as const)\nexport type CountdownProps = ExtractPropTypes<typeof countdownProps>\n\nexport const countdownEmits = {\n  finish: () => true,\n  [CHANGE_EVENT]: (value: number) => isNumber(value),\n}\nexport type CountdownEmits = typeof countdownEmits\n\nexport type CountdownInstance = InstanceType<typeof Countdown> & unknown\n"], "names": ["buildProps", "definePropType", "CHANGE_EVENT", "isNumber"], "mappings": ";;;;;;;;AAEY,MAAC,cAAc,GAAGA,kBAAU,CAAC;AACzC,EAAE,MAAM,EAAE;AACV,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,UAAU;AACvB,GAAG;AACH,EAAE,MAAM,EAAE,MAAM;AAChB,EAAE,MAAM,EAAE,MAAM;AAChB,EAAE,KAAK,EAAE,MAAM;AACf,EAAE,KAAK,EAAE;AACT,IAAI,IAAI,EAAEC,sBAAc,CAAC,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;AAC1C,IAAI,OAAO,EAAE,CAAC;AACd,GAAG;AACH,EAAE,UAAU,EAAE;AACd,IAAI,IAAI,EAAEA,sBAAc,CAAC,CAAC,MAAM,EAAE,MAAM,EAAE,KAAK,CAAC,CAAC;AACjD,GAAG;AACH,CAAC,EAAE;AACS,MAAC,cAAc,GAAG;AAC9B,EAAE,MAAM,EAAE,MAAM,IAAI;AACpB,EAAE,CAACC,kBAAY,GAAG,CAAC,KAAK,KAAKC,cAAQ,CAAC,KAAK,CAAC;AAC5C;;;;;"}