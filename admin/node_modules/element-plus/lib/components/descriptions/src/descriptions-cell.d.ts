import type { PropType } from 'vue';
import type { IDescriptionsInject } from './descriptions.type';
import type { DescriptionItemVNode } from './description-item';
declare const _default: import("vue").DefineComponent<{
    cell: {
        type: PropType<DescriptionItemVNode>;
    };
    tag: {
        type: StringConstructor;
        default: string;
    };
    type: {
        type: StringConstructor;
    };
}, {
    descriptions: IDescriptionsInject;
}, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, Record<string, any>, string, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<import("vue").ExtractPropTypes<{
    cell: {
        type: PropType<DescriptionItemVNode>;
    };
    tag: {
        type: StringConstructor;
        default: string;
    };
    type: {
        type: StringConstructor;
    };
}>>, {
    tag: string;
}>;
export default _default;
