{"version": 3, "file": "cascader2.js", "sources": ["../../../../../../packages/components/cascader/src/cascader.vue"], "sourcesContent": ["<template>\n  <el-tooltip\n    ref=\"tooltipRef\"\n    :visible=\"popperVisible\"\n    :teleported=\"teleported\"\n    :popper-class=\"[nsCascader.e('dropdown'), popperClass]\"\n    :popper-options=\"popperOptions\"\n    :fallback-placements=\"fallbackPlacements\"\n    :stop-popper-mouse-event=\"false\"\n    :gpu-acceleration=\"false\"\n    :placement=\"placement\"\n    :transition=\"`${nsCascader.namespace.value}-zoom-in-top`\"\n    effect=\"light\"\n    pure\n    :persistent=\"persistent\"\n    @hide=\"hideSuggestionPanel\"\n  >\n    <template #default>\n      <div\n        v-clickoutside:[contentRef]=\"() => togglePopperVisible(false)\"\n        :class=\"cascaderKls\"\n        :style=\"cascaderStyle\"\n        @click=\"() => togglePopperVisible(readonly ? undefined : true)\"\n        @keydown=\"handleKeyDown\"\n        @mouseenter=\"inputHover = true\"\n        @mouseleave=\"inputHover = false\"\n      >\n        <el-input\n          ref=\"input\"\n          v-model=\"inputValue\"\n          :placeholder=\"currentPlaceholder\"\n          :readonly=\"readonly\"\n          :disabled=\"isDisabled\"\n          :validate-event=\"false\"\n          :size=\"realSize\"\n          :class=\"inputClass\"\n          :tabindex=\"multiple && filterable && !isDisabled ? -1 : undefined\"\n          @compositionstart=\"handleComposition\"\n          @compositionupdate=\"handleComposition\"\n          @compositionend=\"handleComposition\"\n          @focus=\"handleFocus\"\n          @blur=\"handleBlur\"\n          @input=\"handleInput\"\n        >\n          <template v-if=\"$slots.prefix\" #prefix>\n            <slot name=\"prefix\" />\n          </template>\n          <template #suffix>\n            <el-icon\n              v-if=\"clearBtnVisible\"\n              key=\"clear\"\n              :class=\"[nsInput.e('icon'), 'icon-circle-close']\"\n              @click.stop=\"handleClear\"\n            >\n              <circle-close />\n            </el-icon>\n            <el-icon\n              v-else\n              key=\"arrow-down\"\n              :class=\"cascaderIconKls\"\n              @click.stop=\"togglePopperVisible()\"\n            >\n              <arrow-down />\n            </el-icon>\n          </template>\n        </el-input>\n\n        <div\n          v-if=\"multiple\"\n          ref=\"tagWrapper\"\n          :class=\"[\n            nsCascader.e('tags'),\n            nsCascader.is('validate', Boolean(validateState)),\n          ]\"\n        >\n          <el-tag\n            v-for=\"tag in presentTags\"\n            :key=\"tag.key\"\n            :type=\"tagType\"\n            :size=\"tagSize\"\n            :effect=\"tagEffect\"\n            :hit=\"tag.hitState\"\n            :closable=\"tag.closable\"\n            disable-transitions\n            @close=\"deleteTag(tag)\"\n          >\n            <template v-if=\"tag.isCollapseTag === false\">\n              <span>{{ tag.text }}</span>\n            </template>\n            <template v-else>\n              <el-tooltip\n                :disabled=\"popperVisible || !collapseTagsTooltip\"\n                :fallback-placements=\"['bottom', 'top', 'right', 'left']\"\n                placement=\"bottom\"\n                effect=\"light\"\n              >\n                <template #default>\n                  <span>{{ tag.text }}</span>\n                </template>\n                <template #content>\n                  <el-scrollbar :max-height=\"maxCollapseTagsTooltipHeight\">\n                    <div :class=\"nsCascader.e('collapse-tags')\">\n                      <div\n                        v-for=\"(tag2, idx) in allPresentTags.slice(\n                          maxCollapseTags\n                        )\"\n                        :key=\"idx\"\n                        :class=\"nsCascader.e('collapse-tag')\"\n                      >\n                        <el-tag\n                          :key=\"tag2.key\"\n                          class=\"in-tooltip\"\n                          :type=\"tagType\"\n                          :size=\"tagSize\"\n                          :effect=\"tagEffect\"\n                          :hit=\"tag2.hitState\"\n                          :closable=\"tag2.closable\"\n                          disable-transitions\n                          @close=\"deleteTag(tag2)\"\n                        >\n                          <span>{{ tag2.text }}</span>\n                        </el-tag>\n                      </div>\n                    </div>\n                  </el-scrollbar>\n                </template>\n              </el-tooltip>\n            </template>\n          </el-tag>\n          <input\n            v-if=\"filterable && !isDisabled\"\n            v-model=\"searchInputValue\"\n            type=\"text\"\n            :class=\"nsCascader.e('search-input')\"\n            :placeholder=\"presentText ? '' : inputPlaceholder\"\n            @input=\"(e) => handleInput(searchInputValue, e as KeyboardEvent)\"\n            @click.stop=\"togglePopperVisible(true)\"\n            @keydown.delete=\"handleDelete\"\n            @compositionstart=\"handleComposition\"\n            @compositionupdate=\"handleComposition\"\n            @compositionend=\"handleComposition\"\n            @focus=\"handleFocus\"\n            @blur=\"handleBlur\"\n          />\n        </div>\n      </div>\n    </template>\n\n    <template #content>\n      <el-cascader-panel\n        v-show=\"!filtering\"\n        ref=\"cascaderPanelRef\"\n        v-model=\"checkedValue\"\n        :options=\"options\"\n        :props=\"props.props\"\n        :border=\"false\"\n        :render-label=\"$slots.default\"\n        @expand-change=\"handleExpandChange\"\n        @close=\"$nextTick(() => togglePopperVisible(false))\"\n      >\n        <template #empty>\n          <slot name=\"empty\" />\n        </template>\n      </el-cascader-panel>\n      <el-scrollbar\n        v-if=\"filterable\"\n        v-show=\"filtering\"\n        ref=\"suggestionPanel\"\n        tag=\"ul\"\n        :class=\"nsCascader.e('suggestion-panel')\"\n        :view-class=\"nsCascader.e('suggestion-list')\"\n        @keydown=\"handleSuggestionKeyDown\"\n      >\n        <template v-if=\"suggestions.length\">\n          <li\n            v-for=\"item in suggestions\"\n            :key=\"item.uid\"\n            :class=\"[\n              nsCascader.e('suggestion-item'),\n              nsCascader.is('checked', item.checked),\n            ]\"\n            :tabindex=\"-1\"\n            @click=\"handleSuggestionClick(item)\"\n          >\n            <slot name=\"suggestion-item\" :item=\"item\">\n              <span>{{ item.text }}</span>\n              <el-icon v-if=\"item.checked\">\n                <check />\n              </el-icon>\n            </slot>\n          </li>\n        </template>\n        <slot v-else name=\"empty\">\n          <li :class=\"nsCascader.e('empty-text')\">\n            {{ t('el.cascader.noMatch') }}\n          </li>\n        </slot>\n      </el-scrollbar>\n    </template>\n  </el-tooltip>\n</template>\n\n<script lang=\"ts\" setup>\nimport { computed, nextTick, onMounted, ref, useAttrs, watch } from 'vue'\nimport { cloneDeep, debounce } from 'lodash-unified'\nimport { useCssVar, useResizeObserver } from '@vueuse/core'\nimport {\n  debugWarn,\n  focusNode,\n  getSibling,\n  isClient,\n  isPromise,\n} from '@element-plus/utils'\nimport ElCascaderPanel from '@element-plus/components/cascader-panel'\nimport ElInput from '@element-plus/components/input'\nimport ElTooltip from '@element-plus/components/tooltip'\nimport ElScrollbar from '@element-plus/components/scrollbar'\nimport ElTag from '@element-plus/components/tag'\nimport ElIcon from '@element-plus/components/icon'\nimport { useFormItem, useFormSize } from '@element-plus/components/form'\nimport { ClickOutside as vClickoutside } from '@element-plus/directives'\nimport {\n  useComposition,\n  useEmptyValues,\n  useLocale,\n  useNamespace,\n} from '@element-plus/hooks'\nimport {\n  CHANGE_EVENT,\n  EVENT_CODE,\n  UPDATE_MODEL_EVENT,\n} from '@element-plus/constants'\nimport { ArrowDown, Check, CircleClose } from '@element-plus/icons-vue'\nimport { cascaderEmits, cascaderProps } from './cascader'\n\nimport type { Options } from '@element-plus/components/popper'\nimport type { ComputedRef, Ref, StyleValue } from 'vue'\nimport type { TooltipInstance } from '@element-plus/components/tooltip'\nimport type { InputInstance } from '@element-plus/components/input'\nimport type { ScrollbarInstance } from '@element-plus/components/scrollbar'\nimport type {\n  CascaderNode,\n  CascaderPanelInstance,\n  CascaderValue,\n  Tag,\n} from '@element-plus/components/cascader-panel'\n\nconst popperOptions: Partial<Options> = {\n  modifiers: [\n    {\n      name: 'arrowPosition',\n      enabled: true,\n      phase: 'main',\n      fn: ({ state }) => {\n        const { modifiersData, placement } = state as any\n        if (['right', 'left', 'bottom', 'top'].includes(placement)) return\n        if (modifiersData.arrow) {\n          modifiersData.arrow.x = 35\n        }\n      },\n      requires: ['arrow'],\n    },\n  ],\n}\nconst COMPONENT_NAME = 'ElCascader'\n\ndefineOptions({\n  name: COMPONENT_NAME,\n})\n\nconst props = defineProps(cascaderProps)\nconst emit = defineEmits(cascaderEmits)\nconst attrs = useAttrs()\n\nlet inputInitialHeight = 0\nlet pressDeleteCount = 0\n\nconst nsCascader = useNamespace('cascader')\nconst nsInput = useNamespace('input')\n\nconst { t } = useLocale()\nconst { form, formItem } = useFormItem()\nconst { valueOnClear } = useEmptyValues(props)\nconst { isComposing, handleComposition } = useComposition({\n  afterComposition(event) {\n    const text = (event.target as HTMLInputElement)?.value\n    handleInput(text)\n  },\n})\n\nconst tooltipRef: Ref<TooltipInstance | null> = ref(null)\nconst input: Ref<InputInstance | null> = ref(null)\nconst tagWrapper = ref(null)\nconst cascaderPanelRef: Ref<CascaderPanelInstance | null> = ref(null)\nconst suggestionPanel: Ref<ScrollbarInstance | null> = ref(null)\nconst popperVisible = ref(false)\nconst inputHover = ref(false)\nconst filtering = ref(false)\nconst filterFocus = ref(false)\nconst inputValue = ref('')\nconst searchInputValue = ref('')\nconst presentTags: Ref<Tag[]> = ref([])\nconst allPresentTags: Ref<Tag[]> = ref([])\nconst suggestions: Ref<CascaderNode[]> = ref([])\n\nconst cascaderStyle = computed<StyleValue>(() => {\n  return attrs.style as StyleValue\n})\n\nconst isDisabled = computed(() => props.disabled || form?.disabled)\nconst inputPlaceholder = computed(\n  () => props.placeholder ?? t('el.cascader.placeholder')\n)\nconst currentPlaceholder = computed(() =>\n  searchInputValue.value || presentTags.value.length > 0 || isComposing.value\n    ? ''\n    : inputPlaceholder.value\n)\nconst realSize = useFormSize()\nconst tagSize = computed(() =>\n  realSize.value === 'small' ? 'small' : 'default'\n)\nconst multiple = computed(() => !!props.props.multiple)\nconst readonly = computed(() => !props.filterable || multiple.value)\nconst searchKeyword = computed(() =>\n  multiple.value ? searchInputValue.value : inputValue.value\n)\nconst checkedNodes: ComputedRef<CascaderNode[]> = computed(\n  () => cascaderPanelRef.value?.checkedNodes || []\n)\nconst clearBtnVisible = computed(() => {\n  if (\n    !props.clearable ||\n    isDisabled.value ||\n    filtering.value ||\n    !inputHover.value\n  )\n    return false\n\n  return !!checkedNodes.value.length\n})\nconst presentText = computed(() => {\n  const { showAllLevels, separator } = props\n  const nodes = checkedNodes.value\n  return nodes.length\n    ? multiple.value\n      ? ''\n      : nodes[0].calcText(showAllLevels, separator)\n    : ''\n})\n\nconst validateState = computed(() => formItem?.validateState || '')\n\nconst checkedValue = computed<CascaderValue>({\n  get() {\n    return cloneDeep(props.modelValue) as CascaderValue\n  },\n  set(val) {\n    // https://github.com/element-plus/element-plus/issues/17647\n    const value = val ?? valueOnClear.value\n    emit(UPDATE_MODEL_EVENT, value)\n    emit(CHANGE_EVENT, value)\n    if (props.validateEvent) {\n      formItem?.validate('change').catch((err) => debugWarn(err))\n    }\n  },\n})\n\nconst cascaderKls = computed(() => {\n  return [\n    nsCascader.b(),\n    nsCascader.m(realSize.value),\n    nsCascader.is('disabled', isDisabled.value),\n    attrs.class,\n  ]\n})\n\nconst cascaderIconKls = computed(() => {\n  return [\n    nsInput.e('icon'),\n    'icon-arrow-down',\n    nsCascader.is('reverse', popperVisible.value),\n  ]\n})\n\nconst inputClass = computed(() => {\n  return nsCascader.is('focus', popperVisible.value || filterFocus.value)\n})\n\nconst contentRef = computed(() => {\n  return tooltipRef.value?.popperRef?.contentRef\n})\n\nconst togglePopperVisible = (visible?: boolean) => {\n  if (isDisabled.value) return\n\n  visible = visible ?? !popperVisible.value\n\n  if (visible !== popperVisible.value) {\n    popperVisible.value = visible\n    input.value?.input?.setAttribute('aria-expanded', `${visible}`)\n\n    if (visible) {\n      updatePopperPosition()\n      nextTick(cascaderPanelRef.value?.scrollToExpandingNode)\n    } else if (props.filterable) {\n      syncPresentTextValue()\n    }\n\n    emit('visibleChange', visible)\n  }\n}\n\nconst updatePopperPosition = () => {\n  nextTick(() => {\n    tooltipRef.value?.updatePopper()\n  })\n}\nconst hideSuggestionPanel = () => {\n  filtering.value = false\n}\n\nconst genTag = (node: CascaderNode): Tag => {\n  const { showAllLevels, separator } = props\n  return {\n    node,\n    key: node.uid,\n    text: node.calcText(showAllLevels, separator),\n    hitState: false,\n    closable: !isDisabled.value && !node.isDisabled,\n    isCollapseTag: false,\n  }\n}\n\nconst deleteTag = (tag: Tag) => {\n  const node = tag.node as CascaderNode\n  node.doCheck(false)\n  cascaderPanelRef.value?.calculateCheckedValue()\n  emit('removeTag', node.valueByOption)\n}\n\nconst calculatePresentTags = () => {\n  if (!multiple.value) return\n\n  const nodes = checkedNodes.value\n  const tags: Tag[] = []\n\n  const allTags: Tag[] = []\n  nodes.forEach((node) => allTags.push(genTag(node)))\n  allPresentTags.value = allTags\n\n  if (nodes.length) {\n    nodes\n      .slice(0, props.maxCollapseTags)\n      .forEach((node) => tags.push(genTag(node)))\n    const rest = nodes.slice(props.maxCollapseTags)\n    const restCount = rest.length\n\n    if (restCount) {\n      if (props.collapseTags) {\n        tags.push({\n          key: -1,\n          text: `+ ${restCount}`,\n          closable: false,\n          isCollapseTag: true,\n        })\n      } else {\n        rest.forEach((node) => tags.push(genTag(node)))\n      }\n    }\n  }\n\n  presentTags.value = tags\n}\n\nconst calculateSuggestions = () => {\n  const { filterMethod, showAllLevels, separator } = props\n  const res = cascaderPanelRef.value\n    ?.getFlattedNodes(!props.props.checkStrictly)\n    ?.filter((node) => {\n      if (node.isDisabled) return false\n      node.calcText(showAllLevels, separator)\n      return filterMethod(node, searchKeyword.value)\n    })\n\n  if (multiple.value) {\n    presentTags.value.forEach((tag) => {\n      tag.hitState = false\n    })\n    allPresentTags.value.forEach((tag) => {\n      tag.hitState = false\n    })\n  }\n\n  filtering.value = true\n  suggestions.value = res!\n  updatePopperPosition()\n}\n\nconst focusFirstNode = () => {\n  let firstNode!: HTMLElement\n\n  if (filtering.value && suggestionPanel.value) {\n    firstNode = suggestionPanel.value.$el.querySelector(\n      `.${nsCascader.e('suggestion-item')}`\n    )\n  } else {\n    firstNode = cascaderPanelRef.value?.$el.querySelector(\n      `.${nsCascader.b('node')}[tabindex=\"-1\"]`\n    )\n  }\n\n  if (firstNode) {\n    firstNode.focus()\n    !filtering.value && firstNode.click()\n  }\n}\n\nconst updateStyle = () => {\n  const inputInner = input.value?.input\n  const tagWrapperEl = tagWrapper.value\n  const suggestionPanelEl = suggestionPanel.value?.$el\n\n  if (!isClient || !inputInner) return\n\n  if (suggestionPanelEl) {\n    const suggestionList = suggestionPanelEl.querySelector(\n      `.${nsCascader.e('suggestion-list')}`\n    )\n    suggestionList.style.minWidth = `${inputInner.offsetWidth}px`\n  }\n\n  if (tagWrapperEl) {\n    const { offsetHeight } = tagWrapperEl\n    // 2 is el-input__wrapper padding\n    const height =\n      presentTags.value.length > 0\n        ? `${Math.max(offsetHeight, inputInitialHeight) - 2}px`\n        : `${inputInitialHeight}px`\n    inputInner.style.height = height\n    updatePopperPosition()\n  }\n}\n\nconst getCheckedNodes = (leafOnly: boolean) => {\n  return cascaderPanelRef.value?.getCheckedNodes(leafOnly)\n}\n\nconst handleExpandChange = (value: CascaderValue) => {\n  updatePopperPosition()\n  emit('expandChange', value)\n}\n\nconst handleKeyDown = (e: KeyboardEvent) => {\n  if (isComposing.value) return\n\n  switch (e.code) {\n    case EVENT_CODE.enter:\n    case EVENT_CODE.numpadEnter:\n      togglePopperVisible()\n      break\n    case EVENT_CODE.down:\n      togglePopperVisible(true)\n      nextTick(focusFirstNode)\n      e.preventDefault()\n      break\n    case EVENT_CODE.esc:\n      if (popperVisible.value === true) {\n        e.preventDefault()\n        e.stopPropagation()\n        togglePopperVisible(false)\n      }\n      break\n    case EVENT_CODE.tab:\n      togglePopperVisible(false)\n      break\n  }\n}\n\nconst handleClear = () => {\n  cascaderPanelRef.value?.clearCheckedNodes()\n  if (!popperVisible.value && props.filterable) {\n    syncPresentTextValue()\n  }\n  togglePopperVisible(false)\n  emit('clear')\n}\n\nconst syncPresentTextValue = () => {\n  const { value } = presentText\n  inputValue.value = value\n  searchInputValue.value = value\n}\n\nconst handleSuggestionClick = (node: CascaderNode) => {\n  const { checked } = node\n\n  if (multiple.value) {\n    cascaderPanelRef.value?.handleCheckChange(node, !checked, false)\n  } else {\n    !checked && cascaderPanelRef.value?.handleCheckChange(node, true, false)\n    togglePopperVisible(false)\n  }\n}\n\nconst handleSuggestionKeyDown = (e: KeyboardEvent) => {\n  const target = e.target as HTMLElement\n  const { code } = e\n\n  switch (code) {\n    case EVENT_CODE.up:\n    case EVENT_CODE.down: {\n      e.preventDefault()\n      const distance = code === EVENT_CODE.up ? -1 : 1\n      focusNode(\n        getSibling(\n          target,\n          distance,\n          `.${nsCascader.e('suggestion-item')}[tabindex=\"-1\"]`\n        ) as HTMLElement\n      )\n      break\n    }\n    case EVENT_CODE.enter:\n    case EVENT_CODE.numpadEnter:\n      target.click()\n      break\n  }\n}\n\nconst handleDelete = () => {\n  const tags = presentTags.value\n  const lastTag = tags[tags.length - 1]\n  pressDeleteCount = searchInputValue.value ? 0 : pressDeleteCount + 1\n\n  if (!lastTag || !pressDeleteCount || (props.collapseTags && tags.length > 1))\n    return\n\n  if (lastTag.hitState) {\n    deleteTag(lastTag)\n  } else {\n    lastTag.hitState = true\n  }\n}\n\nconst handleFocus = (e: FocusEvent) => {\n  const el = e.target as HTMLInputElement\n  const name = nsCascader.e('search-input')\n  if (el.className === name) {\n    filterFocus.value = true\n  }\n  emit('focus', e)\n}\n\nconst handleBlur = (e: FocusEvent) => {\n  filterFocus.value = false\n  emit('blur', e)\n}\n\nconst handleFilter = debounce(() => {\n  const { value } = searchKeyword\n\n  if (!value) return\n\n  const passed = props.beforeFilter(value)\n\n  if (isPromise(passed)) {\n    passed.then(calculateSuggestions).catch(() => {\n      /* prevent log error */\n    })\n  } else if (passed !== false) {\n    calculateSuggestions()\n  } else {\n    hideSuggestionPanel()\n  }\n}, props.debounce)\n\nconst handleInput = (val: string, e?: KeyboardEvent) => {\n  !popperVisible.value && togglePopperVisible(true)\n\n  if (e?.isComposing) return\n\n  val ? handleFilter() : hideSuggestionPanel()\n}\n\nconst getInputInnerHeight = (inputInner: HTMLElement): number =>\n  Number.parseFloat(\n    useCssVar(nsInput.cssVarName('input-height'), inputInner).value\n  ) - 2\n\nwatch(filtering, updatePopperPosition)\n\nwatch(\n  [checkedNodes, isDisabled, () => props.collapseTags],\n  calculatePresentTags\n)\n\nwatch(presentTags, () => {\n  nextTick(() => updateStyle())\n})\n\nwatch(realSize, async () => {\n  await nextTick()\n  const inputInner = input.value!.input!\n  inputInitialHeight = getInputInnerHeight(inputInner) || inputInitialHeight\n  updateStyle()\n})\n\nwatch(presentText, syncPresentTextValue, { immediate: true })\n\nonMounted(() => {\n  const inputInner = input.value!.input!\n\n  const inputInnerHeight = getInputInnerHeight(inputInner)\n\n  inputInitialHeight = inputInner.offsetHeight || inputInnerHeight\n  useResizeObserver(inputInner, updateStyle)\n})\n\ndefineExpose({\n  /**\n   * @description get an array of currently selected node,(leafOnly) whether only return the leaf checked nodes, default is `false`\n   */\n  getCheckedNodes,\n  /**\n   * @description cascader panel ref\n   */\n  cascaderPanelRef,\n  /**\n   * @description toggle the visible of popper\n   */\n  togglePopperVisible,\n  /**\n   * @description cascader content ref\n   */\n  contentRef,\n  /**\n   * @description selected content text\n   */\n  presentText,\n})\n</script>\n"], "names": ["useAttrs", "useNamespace", "useLocale", "useFormItem", "useEmptyValues", "useComposition", "ref", "computed", "useFormSize", "UPDATE_MODEL_EVENT", "CHANGE_EVENT", "debugWarn", "nextTick", "isClient", "EVENT_CODE", "focusNode", "getSibling", "debounce", "isPromise", "useCssVar", "watch", "onMounted", "useResizeObserver", "_openBlock", "_createBlock", "_unref", "ElTooltip"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;uCA0Qc,CAAA;AAAA,EACZ,IAAM,EAAA,cAAA;AACR;;;;;;;AArBA,IAAA,MAAM,aAAkC,GAAA;AAAA,MACtC,SAAW,EAAA;AAAA,QACT;AAAA,UACE,IAAM,EAAA,eAAA;AAAA,UACN,OAAS,EAAA,IAAA;AAAA,UACT,KAAO,EAAA,MAAA;AAAA,UACP,EAAI,EAAA,CAAC,EAAE,KAAA,EAAY,KAAA;AACjB,YAAM,MAAA,EAAE,aAAe,EAAA,SAAA,EAAc,GAAA,KAAA,CAAA;AACrC,YAAI,IAAA,CAAC,SAAS,MAAQ,EAAA,QAAA,EAAU,KAAK,CAAE,CAAA,QAAA,CAAS,SAAS,CAAG;AAC5D,cAAA;AACE,YAAA,IAAA,mBAAwB,EAAA;AAAA,cAC1B,aAAA,CAAA,KAAA,CAAA,CAAA,GAAA,EAAA,CAAA;AAAA,aACF;AAAA,WACA;AAAkB,UACpB,QAAA,EAAA,CAAA,OAAA,CAAA;AAAA,SACF;AAAA,OACF;AASA,KAAA,CAAA;AAEA,IAAA,MAAyB,KAAA,GAAAA,YAAA,EAAA,CAAA;AACzB,IAAA,IAAI,kBAAmB,GAAA,CAAA,CAAA;AAEvB,IAAM,IAAA;AACN,IAAM,MAAA,UAAU,qBAAoB,CAAA,UAAA,CAAA,CAAA;AAEpC,IAAM,MAAA,OAAI,GAAcC,kBAAA,CAAA,OAAA,CAAA,CAAA;AACxB,IAAA,MAAM,EAAE,CAAA,EAAA,GAAMC,iBAAS,EAAA,CAAA;AACvB,IAAA,MAAM,EAAE,IAAA,EAAA,QAAiB,EAAA,GAAAC,uBAAA,EAAe,CAAK;AAC7C,IAAA,MAAM,EAAE,YAAA,EAA+B,GAAAC,sBAAA,CAAA,KAAmB,CAAA,CAAA;AAAA,IAAA,mBACvC,EAAO,iBAAA,EAAA,GAAAC,sBAAA,CAAA;AACtB,MAAM,sBAA2C,EAAA;AACjD,QAAA,IAAA,EAAA,CAAA;AAAgB,QAClB,MAAA,IAAA,GAAA,CAAA,EAAA,GAAA,KAAA,CAAA,MAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,KAAA,CAAA;AAAA,QACD,WAAA,CAAA,IAAA,CAAA,CAAA;AAED,OAAM;AACN,KAAM,CAAA,CAAA;AACN,IAAM,MAAA,UAAA,GAAaC,QAAI,IAAI,CAAA,CAAA;AAC3B,IAAM,MAAA,KAAA,GAAAA,OAAA,CAAA,IAAA,CAAA,CAAA;AACN,IAAM,MAAA,UAAA,GAAAA,OAAA,CAAA,KAAqD,CAAI;AAC/D,IAAM,MAAA,gBAAgB,UAAS,CAAA,IAAA,CAAA,CAAA;AAC/B,IAAM,MAAA,kBAAsBA,OAAA,CAAA,IAAA,CAAA,CAAA;AAC5B,IAAM,MAAA,gBAAgBA,OAAK,CAAA,KAAA,CAAA,CAAA;AAC3B,IAAM,MAAA,UAAA,GAAAA,aAAuB,CAAA,CAAA;AAC7B,IAAM,MAAA,SAAA,GAAAA,aAAmB,CAAA,CAAA;AACzB,IAAM,MAAA,WAAA,GAAAA,OAAA,CAAA,KAAuB,CAAE,CAAA;AAC/B,IAAM,MAAA,UAAA,GAAAA,OAA8B,CAAA,EAAC,CAAC,CAAA;AACtC,IAAM,MAAA,gBAAA,GAAiCA,OAAC,CAAC,EAAA,CAAA,CAAA;AACzC,IAAM,MAAA,WAAA,GAAmCA,OAAI,CAAA,EAAE,CAAA,CAAA;AAE/C,IAAM,MAAA,cAAA,eAAqC;AACzC,IAAA,MAAA,WAAa,GAAAA,OAAA,CAAA,EAAA,CAAA,CAAA;AAAA,IACf,MAAC,aAAA,GAAAC,YAAA,CAAA,MAAA;AAED,MAAA,kBAA4B,CAAA;AAC5B,KAAA,CAAA,CAAA;AAAyB,IAAA,MACjB,UAAqB,GAAAA,YAAA,CAAA,MAA2B,KAAA,CAAA,QAAA,KAAA,IAAA,IAAA,IAAA,GAAA,KAAA,CAAA,GAAA,IAAA,CAAA,QAAA,CAAA,CAAA,CAAA;AAAA,IACxD,MAAA,gBAAA,GAAAA,YAAA,CAAA,MAAA;AACA,MAAA,IAAM,EAAqB,CAAA;AAAA,MAAS,OACjB,CAAA,EAAA,GAAA,KAAA,CAAA,WAAA,KAAqB,IAAA,GAAA,EAAA,GAAA,CAAA,CAAA,yBAAoB,CAAA,CAAY;AAEjD,KACvB,CAAA,CAAA;AACA,IAAA,MAAM,kBAAuB,GAAAA,YAAA,CAAA,MAAA,gBAAA,CAAA,KAAA,IAAA,WAAA,CAAA,KAAA,CAAA,MAAA,GAAA,CAAA,IAAA,WAAA,CAAA,KAAA,GAAA,EAAA,GAAA,gBAAA,CAAA,KAAA,CAAA,CAAA;AAC7B,IAAA,MAAM,QAAU,GAAAC,8BAAA,EAAA,CAAA;AAAA,IAAA,MACd,OAAA,GAAAD,YAAmB,CAAA,MAAA,QAAoB,CAAA,KAAA,KAAA,OAAA,GAAA,OAAA,GAAA,SAAA,CAAA,CAAA;AAAA,IACzC,MAAA,QAAA,GAAAA,YAAA,CAAA,MAAA,CAAA,CAAA,KAAA,CAAA,KAAA,CAAA,QAAA,CAAA,CAAA;AACA,IAAA,MAAM,WAAWA,YAAS,CAAA,MAAM,CAAC,KAAC,CAAA,UAAoB,IAAA,QAAA,CAAA,KAAA,CAAA,CAAA;AACtD,IAAA,MAAM,aAAoB,GAAAA,YAAA,CAAA,MAAa,QAAA,CAAA,KAAA,mBAA4B,CAAA,KAAA,GAAA,UAAA,CAAA,KAAA,CAAA,CAAA;AACnE,IAAA,MAAM,YAAgB,GAAAA,YAAA,CAAA,MAAA;AAAA,MAAS,IAC7B,EAAA,CAAA;AAAqD,MACvD,OAAA,CAAA,CAAA,EAAA,GAAA,gBAAA,CAAA,KAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,YAAA,KAAA,EAAA,CAAA;AACA,KAAA,CAAA,CAAA;AAAkD,IAAA,MAC1C,eAAA,GAAAA,YAAwB,CAAA,MAAA;AAAiB,MACjD,IAAA,CAAA,KAAA,CAAA,SAAA,IAAA,UAAA,CAAA,KAAA,IAAA,SAAA,CAAA,KAAA,IAAA,CAAA,UAAA,CAAA,KAAA;AACA,QAAM,OAAA,KAAA,CAAA;AACJ,MACE,qBACA,CAAA,KAAA,CAAA,MAAA,CAAW;AAIX,KAAO,CAAA,CAAA;AAET,IAAO,MAAA,WAAE,GAAAA,YAAmB,CAAA,MAAA;AAAA,MAC7B,MAAA,EAAA,aAAA,EAAA,SAAA,EAAA,GAAA,KAAA,CAAA;AACD,MAAM,MAAA,KAAA,GAAA,aAAuB,KAAM,CAAA;AACjC,MAAM,OAAA,KAAiB,CAAA,MAAA,GAAA,QAAA,CAAA,KAAc,GAAA,EAAA,GAAA,KAAA,CAAA,CAAA,CAAA,CAAA,QAAA,CAAA,aAAA,EAAA,SAAA,CAAA,GAAA,EAAA,CAAA;AACrC,KAAA,CAAA,CAAA;AACA,IAAO,MAAA,aACH,GAAAA,YAAA,CAAA,MACE,CAAA,QACA,IAAM,IAAG,GAAA,KAAA,CAAA,GAAwB,QAAA,CAAA,aAAS,KAC5C,EAAA,CAAA,CAAA;AAAA,IACN,MAAC,YAAA,GAAAA,YAAA,CAAA;AAED,MAAA,GAAA,GAAsB;AAEtB,QAAA,+BAAqB,KAAwB,CAAA,UAAA,CAAA,CAAA;AAAA,OACrC;AACJ,MAAO,GAAA,CAAA,GAAA,EAAA;AAA0B,QACnC,MAAA,KAAA,GAAA,GAAA,IAAA,IAAA,GAAA,GAAA,GAAA,YAAA,CAAA,KAAA,CAAA;AAAA,YACS,CAAAE,wBAAA,EAAA,KAAA,CAAA,CAAA;AAEP,QAAM,IAAA,CAAAC,yBAA4B,CAAA,CAAA;AAClC,QAAA,IAAA,qBAAyB;AACzB,UAAA,mBAAwB,KAAA,CAAA,GAAA,QAAA,CAAA,QAAA,CAAA,QAAA,CAAA,CAAA,KAAA,CAAA,CAAA,GAAA,KAAAC,eAAA,CAAA,GAAA,CAAA,CAAA,CAAA;AACxB,SAAA;AACE,OAAU;AAAgD,KAC5D,CAAA,CAAA;AAAA,IACF,MAAA,WAAA,GAAAJ,YAAA,CAAA,MAAA;AAAA,MACD,OAAA;AAED,QAAM,UAAA,CAAA,CAAA,EAAA;AACJ,QAAO,UAAA,CAAA,CAAA,CAAA,QAAA,CAAA,KAAA,CAAA;AAAA,QACL,WAAW,EAAE,CAAA,UAAA,EAAA,UAAA,CAAA,KAAA,CAAA;AAAA,QACb,KAAA,CAAA,KAAW;AAAgB,OAAA,CAC3B;AAA0C,KAAA,CAAA,CAAA;AACpC,IACR,MAAA,eAAA,GAAAA,YAAA,CAAA,MAAA;AAAA,MACD,OAAA;AAED,QAAM,OAAA,CAAA,CAAA,CAAA,MAAA,CAAA;AACJ,QAAO,iBAAA;AAAA,QACL,UAAU,CAAM,EAAA,CAAA,SAAA,EAAA,aAAA,CAAA,KAAA,CAAA;AAAA,OAChB,CAAA;AAAA,KAAA,CAAA,CAAA;AAC4C,IAC9C,MAAA,UAAA,GAAAA,YAAA,CAAA,MAAA;AAAA,MACD,OAAA,UAAA,CAAA,EAAA,CAAA,OAAA,EAAA,aAAA,CAAA,KAAA,IAAA,WAAA,CAAA,KAAA,CAAA,CAAA;AAED,KAAM,CAAA,CAAA;AACJ,IAAA,MAAA,yBAAqB,CAAA,MAAuB;AAA0B,MACvE,IAAA,EAAA,EAAA,EAAA,CAAA;AAED,MAAM,OAAA,CAAA,EAAA,GAAA,CAAA,EAAA,aAA4B,CAAA,KAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,SAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,UAAA,CAAA;AAChC,KAAO,CAAA,CAAA;AAA6B,IACtC,MAAC,mBAAA,GAAA,CAAA,OAAA,KAAA;AAED,MAAM,IAAA,EAAA,EAAA,EAAA,EAAA,EAAA,CAAA;AACJ,MAAA,IAAI,WAAW,KAAO;AAEtB,QAAU,OAAA;AAEV,MAAI,OAAA,GAAA,yBAAiC,GAAA,CAAA,aAAA,CAAA,KAAA,CAAA;AACnC,MAAA,IAAA,OAAA,KAAc,aAAQ,CAAA,KAAA,EAAA;AACtB,QAAA,aAAa,CAAO,KAAA,GAAA,OAAA,CAAA;AAEpB,QAAA,CAAA,EAAA,GAAa,CAAA,EAAA,GAAA,KAAA,CAAA,KAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,KAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,YAAA,CAAA,eAAA,EAAA,CAAA,EAAA,OAAA,CAAA,CAAA,CAAA,CAAA;AACX,QAAqB,IAAA,OAAA,EAAA;AACrB,UAAS,oBAAA,EAAA,CAAA;AAA6C,UACxDK,YAAA,CAAW,MAAM,gBAAY,CAAA,KAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,qBAAA,CAAA,CAAA;AAC3B,SAAqB,MAAA,IAAA,KAAA,CAAA,UAAA,EAAA;AAAA,UACvB,oBAAA,EAAA,CAAA;AAEA,SAAA;AAA6B,QAC/B,IAAA,CAAA,eAAA,EAAA,OAAA,CAAA,CAAA;AAAA,OACF;AAEA,KAAA,CAAA;AACE,IAAA,MAAA,oBAAe,GAAA,MAAA;AACb,MAAAA,YAAA,CAAA;AAA+B,QAChC,IAAA,EAAA,CAAA;AAAA,QACH,CAAA,EAAA,GAAA,UAAA,CAAA,KAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,YAAA,EAAA,CAAA;AACA,OAAA,CAAA,CAAA;AACE,KAAA,CAAA;AAAkB,IACpB,MAAA,mBAAA,GAAA,MAAA;AAEA,MAAM,SAAA,CAAA,KAAsC,GAAA,KAAA,CAAA;AAC1C,KAAM,CAAA;AACN,IAAO,MAAA,MAAA,GAAA,CAAA,IAAA,KAAA;AAAA,MACL,MAAA,EAAA,aAAA,EAAA,SAAA,EAAA,GAAA,KAAA,CAAA;AAAA,MAAA,OACK;AAAK,QACV,IAAM;AAAsC,QAC5C,GAAU,EAAA,IAAA,CAAA,GAAA;AAAA,QACV,IAAU,EAAA,IAAA,CAAC,QAAW,CAAA,aAAe,EAAA,SAAA,CAAA;AAAA,QACrC,QAAe,EAAA,KAAA;AAAA,QACjB,QAAA,EAAA,CAAA,UAAA,CAAA,KAAA,IAAA,CAAA,IAAA,CAAA,UAAA;AAAA,QACF,aAAA,EAAA,KAAA;AAEA,OAAM,CAAA;AACJ,KAAA,CAAA;AACA,IAAA,MAAA,SAAa,GAAK,CAAA,GAAA,KAAA;AAClB,MAAA,IAAA,EAAA,CAAA;AACA,MAAK,MAAA,IAAA,GAAA,GAAA,CAAA;AAA+B,MACtC,IAAA,CAAA,OAAA,CAAA,KAAA,CAAA,CAAA;AAEA,MAAA,CAAA,EAAA,yBAAmC,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,qBAAA,EAAA,CAAA;AACjC,MAAI,IAAA,CAAC,WAAgB,EAAA,IAAA,CAAA,aAAA,CAAA,CAAA;AAErB,KAAA,CAAA;AACA,IAAA,MAAA,oBAAqB,GAAA,MAAA;AAErB,MAAA,IAAA,CAAA,cAAwB;AACxB,QAAM,OAAA;AACN,MAAA,MAAA,KAAA,GAAA,YAAuB,CAAA,KAAA,CAAA;AAEvB,MAAA,UAAU,GAAQ,EAAA,CAAA;AAChB,MAAA,MAAA,OACS,GAAA,EAAS,CAAA;AAElB,MAAA,KAAA,CAAA,OAAa,CAAA,CAAA,IAAA,KAAY,OAAA,CAAA,IAAqB,CAAA,MAAA,CAAA,IAAA,CAAA,CAAA,CAAA,CAAA;AAC9C,MAAA,oBAAkB,GAAK,OAAA,CAAA;AAEvB,MAAA,IAAA,KAAe,CAAA,MAAA,EAAA;AACb,QAAA,KAAA,CAAI,MAAM,CAAc,EAAA,KAAA,CAAA,eAAA,CAAA,CAAA,OAAA,CAAA,CAAA,IAAA,KAAA,IAAA,CAAA,IAAA,CAAA,MAAA,CAAA,IAAA,CAAA,CAAA,CAAA,CAAA;AACtB,QAAA,MAAA,IAAU,GAAA,KAAA,CAAA,KAAA,CAAA,KAAA,CAAA,eAAA,CAAA,CAAA;AAAA,QAAA,MACR,SAAK,GAAA,IAAA,CAAA,MAAA,CAAA;AAAA,QACL,IAAA;AAAoB,UAAA,IACpB,KAAU,CAAA,YAAA,EAAA;AAAA,YAAA,IACK,CAAA,IAAA,CAAA;AAAA,cAChB,GAAA,EAAA,CAAA,CAAA;AAAA,cACI,IAAA,EAAA,CAAA,EAAA,EAAA,SAAA,CAAA,CAAA;AACL,cAAK,QAAA,EAAA,KAAkB;AAAuB,cAChD,aAAA,EAAA,IAAA;AAAA,aACF,CAAA,CAAA;AAAA,WACF,MAAA;AAEA,YAAA,IAAA,CAAA,OAAoB,CAAA,CAAA,IAAA,KAAA,IAAA,CAAA,IAAA,CAAA,MAAA,CAAA,IAAA,CAAA,CAAA,CAAA,CAAA;AAAA,WACtB;AAEA,SAAA;AACE,OAAA;AACA,MAAM,WAAA,CAAM,KAAiB,GAAA,IAAA,CAAA;AAGzB,KAAI,CAAA;AACJ,IAAK,MAAA,6BAAiC;AACtC,MAAO,IAAA,EAAA,EAAA,EAAA,CAAA;AAAsC,MAC/C,MAAC,EAAA,YAAA,EAAA,aAAA,EAAA,SAAA,EAAA,GAAA,KAAA,CAAA;AAEH,MAAA,aAAa,EAAO,GAAA,CAAA,EAAA,GAAA,gBAAA,CAAA,KAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,eAAA,CAAA,CAAA,KAAA,CAAA,KAAA,CAAA,aAAA,CAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,MAAA,CAAA,CAAA,IAAA,KAAA;AAClB,QAAY,IAAA,IAAA,CAAA,UAAc;AACxB,UAAA,OAAe,KAAA,CAAA;AAAA,QACjB,IAAC,CAAA,QAAA,CAAA,aAAA,EAAA,SAAA,CAAA,CAAA;AACD,QAAe,OAAA,YAAA,CAAA,IAAc,EAAA,aAAS,CAAA,KAAA,CAAA,CAAA;AACpC,OAAA,CAAA,CAAA;AAAe,MAAA,IAChB,QAAA,CAAA,KAAA,EAAA;AAAA,QACH,WAAA,CAAA,KAAA,CAAA,OAAA,CAAA,CAAA,GAAA,KAAA;AAEA,UAAA,GAAA,CAAA,QAAkB,GAAA,KAAA,CAAA;AAClB,SAAA,CAAA,CAAA;AACA,QAAqB,cAAA,CAAA,KAAA,CAAA,OAAA,CAAA,CAAA,GAAA,KAAA;AAAA,UACvB,GAAA,CAAA,QAAA,GAAA,KAAA,CAAA;AAEA,SAAA,CAAM;AACJ,OAAI;AAEJ,MAAI,SAAA,CAAA,KAAmB,GAAA,IAAA,CAAA;AACrB,MAAY,WAAA,CAAA,KAAA,GAAA,GAAA,CAAA;AAA0B,MAAA,oBACnB,EAAA,CAAA;AAAkB,KACrC,CAAA;AAAA,IAAA,MACK,cAAA,GAAA,MAAA;AACL,MAAY,IAAA,EAAA,CAAA;AAA4B,MAAA,IACtC,SAAI,CAAA;AAAoB,MAC1B,IAAA,SAAA,CAAA,KAAA,IAAA,eAAA,CAAA,KAAA,EAAA;AAAA,QACF,SAAA,GAAA,eAAA,CAAA,KAAA,CAAA,GAAA,CAAA,aAAA,CAAA,CAAA,CAAA,EAAA,UAAA,CAAA,CAAA,CAAA,iBAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AAEA,OAAA,MAAe;AACb,QAAA,SAAA,GAAgB,CAAA,EAAA,GAAA,gBAAA,CAAA,KAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,GAAA,CAAA,aAAA,CAAA,CAAA,CAAA,EAAA,UAAA,CAAA,CAAA,CAAA,MAAA,CAAA,CAAA,eAAA,CAAA,CAAA,CAAA;AAChB,OAAC;AAAmC,MACtC,IAAA,SAAA,EAAA;AAAA,QACF,SAAA,CAAA,KAAA,EAAA,CAAA;AAEA,QAAA,CAAA,eAAoB,IAAM,SAAA,CAAA,KAAA,EAAA,CAAA;AACxB,OAAM;AACN,KAAA,CAAA;AACA,IAAM,MAAA,WAAA,GAAA,MAAA;AAEN,MAAI,IAAA,EAAa,EAAA,EAAA,CAAA;AAEjB,MAAA,MAAuB,UAAA,GAAA,CAAA,EAAA,GAAA,KAAA,CAAA,KAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,KAAA,CAAA;AACrB,MAAA,MAAA,yBAAyC,CAAA,KAAA,CAAA;AAAA,MAAA,MACnC,iBAAa,GAAA,CAAA,EAAA,GAAA,eAAkB,CAAA,KAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,GAAA,CAAA;AAAA,MACrC,IAAA,CAAAC,aAAA,IAAA,CAAA,UAAA;AACA,QAAA,OAAA;AAAyD,MAC3D,IAAA,iBAAA,EAAA;AAEA,QAAA,MAAkB,cAAA,GAAA,iBAAA,CAAA,aAAA,CAAA,CAAA,CAAA,EAAA,UAAA,CAAA,CAAA,CAAA,iBAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AAChB,QAAM,qBAAe,QAAI,GAAA,CAAA,EAAA,UAAA,CAAA,WAAA,CAAA,EAAA,CAAA,CAAA;AAEzB,OAAA;AAIA,MAAA,IAAA;AACA,QAAqB,MAAA,EAAA,YAAA,EAAA,GAAA,YAAA,CAAA;AAAA,QACvB,MAAA,MAAA,GAAA,WAAA,CAAA,KAAA,CAAA,MAAA,GAAA,CAAA,GAAA,CAAA,EAAA,IAAA,CAAA,GAAA,CAAA,YAAA,EAAA,kBAAA,CAAA,GAAA,CAAA,CAAA,EAAA,CAAA,GAAA,CAAA,EAAA,kBAAA,CAAA,EAAA,CAAA,CAAA;AAAA,QACF,UAAA,CAAA,KAAA,CAAA,MAAA,GAAA,MAAA,CAAA;AAEA,QAAM,oBAAkB,EAAuB,CAAA;AAC7C,OAAO;AAAgD,KACzD,CAAA;AAEA,IAAM,MAAA,eAAA,GAAA,CAAA,QAA+C,KAAA;AACnD,MAAqB,IAAA,EAAA,CAAA;AACrB,MAAA,6BAA0B,CAAA,KAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,eAAA,CAAA,QAAA,CAAA,CAAA;AAAA,KAC5B,CAAA;AAEA,IAAM,MAAA,kBAAsC,GAAA,CAAA,KAAA,KAAA;AAC1C,MAAA,oBAAuB,EAAA,CAAA;AAEvB,MAAA,IAAA,CAAA,cAAgB,EAAA,KAAA,CAAA,CAAA;AAAA,KAAA,CAAA;AACE,IAAA,mBACA,GAAA,CAAA,CAAA,KAAA;AACd,MAAoB,IAAA,WAAA,CAAA,KAAA;AACpB,QAAA,OAAA;AAAA,MAAA,QACc,CAAA,CAAA,IAAA;AACd,QAAA,KAAAC,eAAA,CAAA,KAAA,CAAoB;AACpB,QAAA,KAAAA,eAAuB,CAAA,WAAA;AACvB,UAAA,mBAAiB,EAAA,CAAA;AACjB,UAAA,MAAA;AAAA,QACF,KAAKA,eAAW,CAAA,IAAA;AACd,UAAI;AACF,UAAAF,YAAiB,CAAA,cAAA,CAAA,CAAA;AACjB,UAAA,CAAA,CAAA,cAAkB,EAAA,CAAA;AAClB,UAAA,MAAA;AAAyB,QAC3B,KAAAE,eAAA,CAAA,GAAA;AACA,UAAA,IAAA,aAAA,CAAA,KAAA,KAAA,IAAA,EAAA;AAAA,aACG,CAAW,cAAA,EAAA,CAAA;AACd,YAAA,CAAA,CAAA,eAAA,EAAyB,CAAA;AACzB,YAAA,mBAAA,CAAA,KAAA,CAAA,CAAA;AAAA,WACJ;AAAA,UACF,MAAA;AAEA,QAAA,qBAAoB,GAAM;AACxB,UAAA,oBAAwB,KAAkB,CAAA,CAAA;AAC1C,UAAI,MAAC;AACH,OAAqB;AAAA,KACvB,CAAA;AACA,IAAA,MAAA,WAAA,GAAA,MAAyB;AACzB,MAAA,IAAA,EAAY,CAAA;AAAA,MACd,CAAA,EAAA,GAAA,gBAAA,CAAA,KAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,iBAAA,EAAA,CAAA;AAEA,MAAA,IAAM,wBAA6B,KAAA,CAAA,UAAA,EAAA;AACjC,QAAM,oBAAY,EAAA,CAAA;AAClB,OAAA;AACA,MAAA,mBAAyB,CAAA,KAAA,CAAA,CAAA;AAAA,MAC3B,IAAA,CAAA,OAAA,CAAA,CAAA;AAEA,KAAM,CAAA;AACJ,IAAM,MAAA,oBAAc,GAAA,MAAA;AAEpB,MAAA,aAAa,EAAO,GAAA,WAAA,CAAA;AAClB,MAAA,UAAA,CAAA,KAAA,GAAiB,KAAO,CAAA;AAAuC,MACjE,gBAAO,CAAA,KAAA,GAAA,KAAA,CAAA;AACL,KAAA,CAAA;AACA,IAAA,MAAA,qBAAyB,GAAA,CAAA,IAAA,KAAA;AAAA,MAC3B,IAAA,EAAA,EAAA,EAAA,CAAA;AAAA,MACF,MAAA,EAAA,OAAA,EAAA,GAAA,IAAA,CAAA;AAEA,MAAM,IAAA,QAAA,CAAA,KAAA,EAAA;AACJ,QAAA,CAAA,EAAA,mBAAiB,CAAA,KAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA,OAAA,EAAA,KAAA,CAAA,CAAA;AACjB,OAAM,MAAA;AAEN,QAAA,CAAA,OAAc,KAAA,CAAA,EAAA,GAAA,gBAAA,CAAA,KAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,IAAA,EAAA,KAAA,CAAA,CAAA,CAAA;AAAA,QACZ,mBAAgB,CAAA,KAAA,CAAA,CAAA;AAAA,OAChB;AACE,KAAA,CAAA;AACA,IAAA,MAAA,uBAAiB,GAAS,CAAW,CAAA,KAAA;AACrC,MAAA,MAAA,MAAA,GAAA,CAAA,CAAA,MAAA,CAAA;AAAA,MACE,MAAA,EAAA,IAAA,EAAA,GAAA,CAAA,CAAA;AAAA,MACE,QAAA,IAAA;AAAA,QACA,KAAAA,eAAA,CAAA,EAAA,CAAA;AAAA,QAAA,KAAAA,eACI,CAAA,IAAA,EAAa;AAAkB,UACrC,CAAA,CAAA,cAAA,EAAA,CAAA;AAAA,UACF,MAAA,QAAA,GAAA,IAAA,KAAAA,eAAA,CAAA,EAAA,GAAA,CAAA,CAAA,GAAA,CAAA,CAAA;AACA,UAAAC,gBAAA,CAAAC,iBAAA,CAAA,MAAA,EAAA,QAAA,EAAA,CAAA,CAAA,EAAA,UAAA,CAAA,CAAA,CAAA,iBAAA,CAAA,CAAA,eAAA,CAAA,CAAA,CAAA,CAAA;AAAA,UACF,MAAA;AAAA;AACgB,QAChB,KAAKF,eAAW,CAAA,KAAA,CAAA;AACd,QAAA,KAAAA,eAAa,CAAA,WAAA;AACb,UAAA,MAAA,CAAA,KAAA,EAAA,CAAA;AAAA,UACJ,MAAA;AAAA,OACF;AAEA,KAAA,CAAA;AACE,IAAA,MAAA,YAAyB,GAAA,MAAA;AACzB,MAAA,MAAM,IAAU,GAAA,WAAU,CAAA,KAAA,CAAA;AAC1B,MAAmB,MAAA,OAAA,GAAA,IAAA,CAAA,IAAA,CAAA,MAAA,GAAA,CAAiB,CAAQ,CAAA;AAE5C,MAAA,gBAAgB,yBAA4B,GAAA,CAAA,GAAA,gBAAqB,GAAS,CAAA,CAAA;AACxE,MAAA,IAAA,CAAA,OAAA,IAAA,CAAA,gBAAA,IAAA,KAAA,CAAA,YAAA,IAAA,IAAA,CAAA,MAAA,GAAA,CAAA;AAEF,QAAA;AACE,MAAA,IAAA,OAAA,CAAU,QAAO,EAAA;AAAA,QACZ,SAAA,CAAA,OAAA,CAAA,CAAA;AACL,OAAA,MAAA;AAAmB,QACrB,OAAA,CAAA,QAAA,GAAA,IAAA,CAAA;AAAA,OACF;AAEA,KAAM,CAAA;AACJ,IAAA,MAAA,WAAa,GAAA,CAAA,CAAA,KAAA;AACb,MAAM,MAAA,EAAA,GAAA,CAAA,CAAO,MAAW,CAAA;AACxB,MAAI,MAAA,iBAAuB,CAAA,CAAA,CAAA,cAAA,CAAA,CAAA;AACzB,MAAA,IAAA,EAAA,CAAA,SAAoB,KAAA,IAAA,EAAA;AAAA,QACtB,WAAA,CAAA,KAAA,GAAA,IAAA,CAAA;AACA,OAAA;AAAe,MACjB,IAAA,CAAA,OAAA,EAAA,CAAA,CAAA,CAAA;AAEA,KAAM,CAAA;AACJ,IAAA,MAAA,UAAoB,GAAA,CAAA,CAAA,KAAA;AACpB,MAAA,iBAAc,GAAA,KAAA,CAAA;AAAA,MAChB,IAAA,CAAA,MAAA,EAAA,CAAA,CAAA,CAAA;AAEA,KAAM,CAAA;AACJ,IAAM,MAAA,YAAY,GAAAG,sBAAA,CAAA,MAAA;AAElB,MAAA,MAAY,EAAA,KAAA,EAAA,GAAA,aAAA,CAAA;AAEZ,MAAM,IAAA,CAAA,KAAA;AAEN,QAAI,OAAA;AACF,MAAA,MAAA,MAAY,GAAA,KAAA,CAAA,YAAA,CAAoB,KAAE,CAAA,CAAA;AAAY,MAAA,IAE7CC,gBAAA,CAAA,MAAA,CAAA,EAAA;AAAA,QACH,MAAA,CAAA,yBAA6B,CAAA,CAAA,KAAA,CAAA,MAAA;AAC3B,SAAqB,CAAA,CAAA;AAAA,OAChB,MAAA,IAAA,MAAA,KAAA,KAAA,EAAA;AACL,QAAoB,oBAAA,EAAA,CAAA;AAAA,OACtB,MAAA;AAAA,2BACe,EAAA,CAAA;AAEjB,OAAM;AACJ,KAAC,EAAA,KAAA,CAAA,QAAc,CAAS,CAAA;AAExB,IAAA,MAAI,WAAgB,GAAA,CAAA,GAAA,EAAA,CAAA,KAAA;AAEpB,MAAM,CAAA,aAAA,CAAA,SAAqC,mBAAA,CAAA,IAAA,CAAA,CAAA;AAAA,MAC7C,IAAA,CAAA,IAAA,IAAA,GAAA,KAAA,CAAA,GAAA,CAAA,CAAA,WAAA;AAEA,QAAM,OAAA;AACG,MACL,kBAAkB,EAAA,GAAA,mBAAyB,EAAA,CAAA;AAAe,KACxD,CAAA;AAEN,IAAA,MAAM,mBAA+B,GAAA,CAAA,UAAA,KAAA,MAAA,CAAA,UAAA,CAAAC,cAAA,CAAA,OAAA,CAAA,UAAA,CAAA,cAAA,CAAA,EAAA,UAAA,CAAA,CAAA,KAAA,CAAA,GAAA,CAAA,CAAA;AAErC,IAAAC,SAAA,CAAA,SAAA,EAAA,oBAAA,CAAA,CAAA;AAAA,IAAAA,SACG,CAAA,CAAA,YAA0B,EAAA,UAAA,EAAA,WAAwB,CAAA,YAAA,CAAA,EAAA,oBAAA,CAAA,CAAA;AAAA,IACnDA,SAAA,CAAA,WAAA,EAAA,MAAA;AAAA,MACFR,YAAA,CAAA,MAAA,WAAA,EAAA,CAAA,CAAA;AAEA,KAAA,CAAA,CAAA;AACE,IAASQ,SAAA,CAAA,QAAA,EAAA;AAAmB,MAC7B,MAAAR,YAAA,EAAA,CAAA;AAED,MAAA,gBAA4B,GAAA,KAAA,CAAA,KAAA,CAAA,KAAA,CAAA;AAC1B,MAAA,kBAAe,GAAA,mBAAA,CAAA,UAAA,CAAA,IAAA,kBAAA,CAAA;AACf,MAAM,WAAA,EAAA,CAAA;AACN,KAAqB,CAAA,CAAA;AACrB,IAAYQ,SAAA,CAAA,WAAA,EAAA,oBAAA,EAAA,EAAA,SAAA,EAAA,IAAA,EAAA,CAAA,CAAA;AAAA,IACdC,aAAC,CAAA,MAAA;AAED,MAAA,MAAmB,UAAA,GAAA,KAAA,CAAA,KAAA,CAAA,KAAA,CAAA;AAEnB,MAAA,MAAA,gBAAgB,GAAA,mBAAA,CAAA,UAAA,CAAA,CAAA;AACd,MAAM,kBAAA,aAA0B,CAAA,YAAA,IAAA,gBAAA,CAAA;AAEhC,MAAMC,sBAAA,CAAA;AAEN,KAAA,CAAA,CAAA;AACA,IAAA,MAAA,CAAA;AAAyC,MAC1C,eAAA;AAED,MAAa,gBAAA;AAAA,MAAA,mBAAA;AAAA,MAAA,UAAA;AAAA,MAAA,WAAA;AAAA,KAIX,CAAA,CAAA;AAAA,IAAA,OAAA,CAAA,IAAA,EAAA,MAAA,KAAA;AAAA,MAAA,OAAAC,aAAA,EAAA,EAAAC,eAAA,CAAAC,SAAA,CAAAC,iBAAA,CAAA,EAAA;AAAA,QAAA,OAAA,EAAA,YAAA;AAAA,QAIA,GAAA,EAAA,UAAA;AAAA,QAAA,OAAA,EAAA,aAAA,CAAA,KAAA;AAAA,QAAA,UAAA,EAAA,IAAA,CAAA,UAAA;AAAA,QAAA,cAAA,EAAA,CAAAD,SAAA,CAAA,UAAA,CAAA,CAAA,CAAA,CAAA,UAAA,CAAA,EAAA,IAAA,CAAA,WAAA,CAAA;AAAA,QAIA,gBAAA,EAAA,aAAA;AAAA,QAAA,qBAAA,EAAA,IAAA,CAAA,kBAAA;AAAA,QAAA,yBAAA,EAAA,KAAA;AAAA,QAAA,kBAAA,EAAA,KAAA;AAAA,QAIA,SAAA,EAAA,IAAA,CAAA,SAAA;AAAA,QAAA,UAAA,EAAA,CAAA,EAAAA,SAAA,CAAA,UAAA,CAAA,CAAA,SAAA,CAAA,KAAA,CAAA,YAAA,CAAA;AAAA,QAAA,MAAA,EAAA,OAAA;AAAA,QAAA,IAAA,EAAA,EAAA;AAAA,QAIA,UAAA,EAAA,IAAA,CAAA,UAAA;AAAA,QACD,MAAA,EAAA,mBAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}