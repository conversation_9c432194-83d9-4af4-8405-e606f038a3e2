'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var colorPicker$1 = require('./src/color-picker2.js');
var colorPicker = require('./src/color-picker.js');
var install = require('../../utils/vue/install.js');

const ElColorPicker = install.withInstall(colorPicker$1["default"]);

exports.colorPickerContextKey = colorPicker.colorPickerContextKey;
exports.colorPickerEmits = colorPicker.colorPickerEmits;
exports.colorPickerProps = colorPicker.colorPickerProps;
exports.ElColorPicker = ElColorPicker;
exports["default"] = ElColorPicker;
//# sourceMappingURL=index.js.map
