import type { ExtractPropTypes } from 'vue';
import type { Dayjs } from 'dayjs';
export declare const panelTimeRangeProps: {
    readonly parsedValue: {
        readonly type: import("vue").PropType<[Dayjs, Dayjs]>;
        readonly required: false;
        readonly validator: ((val: unknown) => boolean) | undefined;
        __epPropKey: true;
    };
    readonly visible: BooleanConstructor;
    readonly actualVisible: import("element-plus/es/utils").EpPropFinalized<BooleanConstructor, unknown, unknown, undefined, boolean>;
    readonly format: import("element-plus/es/utils").EpPropFinalized<StringConstructor, unknown, unknown, "", boolean>;
};
export type PanelTimeRangeProps = ExtractPropTypes<typeof panelTimeRangeProps>;
