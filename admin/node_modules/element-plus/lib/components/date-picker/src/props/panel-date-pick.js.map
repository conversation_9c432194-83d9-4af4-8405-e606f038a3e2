{"version": 3, "file": "panel-date-pick.js", "sources": ["../../../../../../../packages/components/date-picker/src/props/panel-date-pick.ts"], "sourcesContent": ["import { buildProps, definePropType } from '@element-plus/utils'\nimport { panelSharedProps } from './shared'\n\nimport type { ExtractPropTypes } from 'vue'\nimport type { Dayjs } from 'dayjs'\n\nexport const panelDatePickProps = buildProps({\n  ...panelSharedProps,\n  parsedValue: {\n    type: definePropType<Dayjs | Dayjs[]>([Object, Array]),\n  },\n  visible: {\n    type: Boolean,\n  },\n  format: {\n    type: String,\n    default: '',\n  },\n} as const)\n\nexport type PanelDatePickProps = ExtractPropTypes<typeof panelDatePickProps>\n"], "names": ["buildProps", "panelSharedProps", "definePropType"], "mappings": ";;;;;;;AAEY,MAAC,kBAAkB,GAAGA,kBAAU,CAAC;AAC7C,EAAE,GAAGC,uBAAgB;AACrB,EAAE,WAAW,EAAE;AACf,IAAI,IAAI,EAAEC,sBAAc,CAAC,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;AACzC,GAAG;AACH,EAAE,OAAO,EAAE;AACX,IAAI,IAAI,EAAE,OAAO;AACjB,GAAG;AACH,EAAE,MAAM,EAAE;AACV,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,EAAE;AACf,GAAG;AACH,CAAC;;;;"}