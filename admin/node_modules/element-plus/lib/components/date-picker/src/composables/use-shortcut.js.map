{"version": 3, "file": "use-shortcut.js", "sources": ["../../../../../../../packages/components/date-picker/src/composables/use-shortcut.ts"], "sourcesContent": ["import { getCurrentInstance, useAttrs, useSlots } from 'vue'\nimport dayjs from 'dayjs'\nimport { isFunction } from '@element-plus/utils'\n\nimport type { SetupContext } from 'vue'\nimport type { useLocale } from '@element-plus/hooks'\nimport type { RangePickerSharedEmits } from '../props/shared'\n\n// FIXME: extract this to `date-picker.ts`\nexport type Shortcut = {\n  text: string\n  value: [Date, Date] | (() => [Date, Date])\n  onClick?: (ctx: Omit<SetupContext<RangePickerSharedEmits>, 'expose'>) => void\n}\n\nexport const useShortcut = (lang: ReturnType<typeof useLocale>['lang']) => {\n  const { emit } = getCurrentInstance()!\n  const attrs = useAttrs()\n  const slots = useSlots()\n\n  const handleShortcutClick = (shortcut: Shortcut) => {\n    const shortcutValues = isFunction(shortcut.value)\n      ? shortcut.value()\n      : shortcut.value\n\n    if (shortcutValues) {\n      emit('pick', [\n        dayjs(shortcutValues[0]).locale(lang.value),\n        dayjs(shortcutValues[1]).locale(lang.value),\n      ])\n      return\n    }\n    if (shortcut.onClick) {\n      shortcut.onClick({\n        attrs,\n        slots,\n        emit,\n      })\n    }\n  }\n\n  return handleShortcutClick\n}\n"], "names": ["getCurrentInstance", "useAttrs", "useSlots", "isFunction", "dayjs"], "mappings": ";;;;;;;;;;;;AAGY,MAAC,WAAW,GAAG,CAAC,IAAI,KAAK;AACrC,EAAE,MAAM,EAAE,IAAI,EAAE,GAAGA,sBAAkB,EAAE,CAAC;AACxC,EAAE,MAAM,KAAK,GAAGC,YAAQ,EAAE,CAAC;AAC3B,EAAE,MAAM,KAAK,GAAGC,YAAQ,EAAE,CAAC;AAC3B,EAAE,MAAM,mBAAmB,GAAG,CAAC,QAAQ,KAAK;AAC5C,IAAI,MAAM,cAAc,GAAGC,iBAAU,CAAC,QAAQ,CAAC,KAAK,CAAC,GAAG,QAAQ,CAAC,KAAK,EAAE,GAAG,QAAQ,CAAC,KAAK,CAAC;AAC1F,IAAI,IAAI,cAAc,EAAE;AACxB,MAAM,IAAI,CAAC,MAAM,EAAE;AACnB,QAAQC,yBAAK,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC;AACnD,QAAQA,yBAAK,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC;AACnD,OAAO,CAAC,CAAC;AACT,MAAM,OAAO;AACb,KAAK;AACL,IAAI,IAAI,QAAQ,CAAC,OAAO,EAAE;AAC1B,MAAM,QAAQ,CAAC,OAAO,CAAC;AACvB,QAAQ,KAAK;AACb,QAAQ,KAAK;AACb,QAAQ,IAAI;AACZ,OAAO,CAAC,CAAC;AACT,KAAK;AACL,GAAG,CAAC;AACJ,EAAE,OAAO,mBAAmB,CAAC;AAC7B;;;;"}