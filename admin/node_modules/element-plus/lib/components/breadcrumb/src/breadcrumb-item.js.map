{"version": 3, "file": "breadcrumb-item.js", "sources": ["../../../../../../packages/components/breadcrumb/src/breadcrumb-item.ts"], "sourcesContent": ["import { buildProps, definePropType } from '@element-plus/utils'\nimport type { ExtractPropTypes } from 'vue'\nimport type { RouteLocationRaw } from 'vue-router'\n\nexport const breadcrumbItemProps = buildProps({\n  /**\n   * @description target route of the link, same as `to` of `vue-router`\n   */\n  to: {\n    type: definePropType<RouteLocationRaw>([String, Object]),\n    default: '',\n  },\n  /**\n   * @description if `true`, the navigation will not leave a history record\n   */\n  replace: Boolean,\n} as const)\nexport type BreadcrumbItemProps = ExtractPropTypes<typeof breadcrumbItemProps>\n"], "names": ["buildProps", "definePropType"], "mappings": ";;;;;;AACY,MAAC,mBAAmB,GAAGA,kBAAU,CAAC;AAC9C,EAAE,EAAE,EAAE;AACN,IAAI,IAAI,EAAEC,sBAAc,CAAC,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;AAC1C,IAAI,OAAO,EAAE,EAAE;AACf,GAAG;AACH,EAAE,OAAO,EAAE,OAAO;AAClB,CAAC;;;;"}