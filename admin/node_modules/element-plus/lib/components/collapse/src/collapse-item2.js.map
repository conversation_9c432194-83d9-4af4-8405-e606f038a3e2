{"version": 3, "file": "collapse-item2.js", "sources": ["../../../../../../packages/components/collapse/src/collapse-item.vue"], "sourcesContent": ["<template>\n  <div :class=\"rootKls\">\n    <div\n      :id=\"scopedHeadId\"\n      :class=\"headKls\"\n      :aria-expanded=\"isActive\"\n      :aria-controls=\"scopedContentId\"\n      :aria-describedby=\"scopedContentId\"\n      :tabindex=\"disabled ? -1 : 0\"\n      role=\"button\"\n      @click=\"handleHeaderClick\"\n      @keydown.space.enter.stop=\"handleEnterClick\"\n      @focus=\"handleFocus\"\n      @blur=\"focusing = false\"\n    >\n      <span :class=\"itemTitleKls\">\n        <slot name=\"title\" :is-active=\"isActive\">{{ title }}</slot>\n      </span>\n      <slot name=\"icon\" :is-active=\"isActive\">\n        <el-icon :class=\"arrowKls\">\n          <component :is=\"icon\" />\n        </el-icon>\n      </slot>\n    </div>\n\n    <el-collapse-transition>\n      <div\n        v-show=\"isActive\"\n        :id=\"scopedContentId\"\n        role=\"region\"\n        :class=\"itemWrapperKls\"\n        :aria-hidden=\"!isActive\"\n        :aria-labelledby=\"scopedHeadId\"\n      >\n        <div :class=\"itemContentKls\">\n          <slot />\n        </div>\n      </div>\n    </el-collapse-transition>\n  </div>\n</template>\n\n<script lang=\"ts\" setup>\nimport ElCollapseTransition from '@element-plus/components/collapse-transition'\nimport ElIcon from '@element-plus/components/icon'\nimport { collapseItemProps } from './collapse-item'\nimport { useCollapseItem, useCollapseItemDOM } from './use-collapse-item'\n\ndefineOptions({\n  name: 'ElCollapseItem',\n})\n\nconst props = defineProps(collapseItemProps)\nconst {\n  focusing,\n  id,\n  isActive,\n  handleFocus,\n  handleHeaderClick,\n  handleEnterClick,\n} = useCollapseItem(props)\n\nconst {\n  arrowKls,\n  headKls,\n  rootKls,\n  itemTitleKls,\n  itemWrapperKls,\n  itemContentKls,\n  scopedContentId,\n  scopedHeadId,\n} = useCollapseItemDOM(props, { focusing, isActive, id })\n\ndefineExpose({\n  /** @description current collapse-item whether active */\n  isActive,\n})\n</script>\n"], "names": ["useCollapseItem", "useCollapseItemDOM"], "mappings": ";;;;;;;;;;;uCAgDc,CAAA;AAAA,EACZ,IAAM,EAAA,gBAAA;AACR,CAAA,CAAA,CAAA;;;;;;AAGA,IAAM,MAAA;AAAA,MACJ,QAAA;AAAA,MACA,EAAA;AAAA,MACA,QAAA;AAAA,MACA,WAAA;AAAA,MACA,iBAAA;AAAA,MACA,gBAAA;AAAA,KACF,GAAIA,gCAAgB,KAAK,CAAA,CAAA;AAEzB,IAAM,MAAA;AAAA,MACJ,QAAA;AAAA,MACA,OAAA;AAAA,MACA,OAAA;AAAA,MACA,YAAA;AAAA,MACA,cAAA;AAAA,MACA,cAAA;AAAA,MACA,eAAA;AAAA,MACA,YAAA;AAAA,QACEC,kCAAmB,CAAA,KAAA,EAAO,EAAE,QAAU,EAAA,QAAA,EAAU,IAAI,CAAA,CAAA;AAExD,IAAa,MAAA,CAAA;AAAA,MAAA,QAAA;AAAA,KAEX,CAAA,CAAA;AAAA,IACF,OAAC,CAAA,IAAA,EAAA,MAAA,KAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}