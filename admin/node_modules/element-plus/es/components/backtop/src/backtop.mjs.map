{"version": 3, "file": "backtop.mjs", "sources": ["../../../../../../packages/components/backtop/src/backtop.ts"], "sourcesContent": ["import type { ExtractPropTypes } from 'vue'\n\nexport const backtopProps = {\n  /**\n   * @description the button will not show until the scroll height reaches this value.\n   */\n  visibilityHeight: {\n    type: Number,\n    default: 200,\n  },\n  /**\n   * @description the target to trigger scroll.\n   */\n  target: {\n    type: String,\n    default: '',\n  },\n  /**\n   * @description right distance.\n   */\n  right: {\n    type: Number,\n    default: 40,\n  },\n  /**\n   * @description bottom distance.\n   */\n  bottom: {\n    type: Number,\n    default: 40,\n  },\n} as const\nexport type BacktopProps = ExtractPropTypes<typeof backtopProps>\n\nexport const backtopEmits = {\n  click: (evt: MouseEvent) => evt instanceof MouseEvent,\n}\nexport type BacktopEmits = typeof backtopEmits\n"], "names": [], "mappings": "AAAY,MAAC,YAAY,GAAG;AAC5B,EAAE,gBAAgB,EAAE;AACpB,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,GAAG;AAChB,GAAG;AACH,EAAE,MAAM,EAAE;AACV,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,EAAE;AACf,GAAG;AACH,EAAE,KAAK,EAAE;AACT,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,EAAE;AACf,GAAG;AACH,EAAE,MAAM,EAAE;AACV,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,EAAE;AACf,GAAG;AACH,EAAE;AACU,MAAC,YAAY,GAAG;AAC5B,EAAE,KAAK,EAAE,CAAC,GAAG,KAAK,GAAG,YAAY,UAAU;AAC3C;;;;"}