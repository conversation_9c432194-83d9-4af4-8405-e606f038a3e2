{"version": 3, "file": "radio.mjs", "sources": ["../../../../../../packages/components/radio/src/radio.ts"], "sourcesContent": ["import { buildProps, isBoolean, isNumber, isString } from '@element-plus/utils'\nimport { CHANGE_EVENT, UPDATE_MODEL_EVENT } from '@element-plus/constants'\nimport { useSizeProp } from '@element-plus/hooks'\nimport type { ExtractPropTypes } from 'vue'\nimport type Radio from './radio.vue'\n\nexport const radioPropsBase = buildProps({\n  /**\n   * @description binding value\n   */\n  modelValue: {\n    type: [String, Number, Boolean],\n    default: undefined,\n  },\n  /**\n   * @description size of the Radio\n   */\n  size: useSizeProp,\n  /**\n   * @description whether Radio is disabled\n   */\n  disabled: Boolean,\n  /**\n   * @description the label of Radio\n   */\n  label: {\n    type: [String, Number, Boolean],\n    default: undefined,\n  },\n  /**\n   * @description the value of Radio\n   */\n  value: {\n    type: [String, Number, Boolean],\n    default: undefined,\n  },\n  /**\n   * @description native `name` attribute\n   */\n  name: {\n    type: String,\n    default: undefined,\n  },\n})\n\nexport const radioProps = buildProps({\n  ...radioPropsBase,\n  /**\n   * @description whether to add a border around Radio\n   */\n  border: Boolean,\n} as const)\n\nexport const radioEmits = {\n  [UPDATE_MODEL_EVENT]: (val: string | number | boolean | undefined) =>\n    isString(val) || isNumber(val) || isBoolean(val),\n  [CHANGE_EVENT]: (val: string | number | boolean | undefined) =>\n    isString(val) || isNumber(val) || isBoolean(val),\n}\n\nexport type RadioProps = ExtractPropTypes<typeof radioProps>\nexport type RadioEmits = typeof radioEmits\nexport type RadioInstance = InstanceType<typeof Radio> & unknown\n"], "names": [], "mappings": ";;;;;;AAGY,MAAC,cAAc,GAAG,UAAU,CAAC;AACzC,EAAE,UAAU,EAAE;AACd,IAAI,IAAI,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,OAAO,CAAC;AACnC,IAAI,OAAO,EAAE,KAAK,CAAC;AACnB,GAAG;AACH,EAAE,IAAI,EAAE,WAAW;AACnB,EAAE,QAAQ,EAAE,OAAO;AACnB,EAAE,KAAK,EAAE;AACT,IAAI,IAAI,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,OAAO,CAAC;AACnC,IAAI,OAAO,EAAE,KAAK,CAAC;AACnB,GAAG;AACH,EAAE,KAAK,EAAE;AACT,IAAI,IAAI,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,OAAO,CAAC;AACnC,IAAI,OAAO,EAAE,KAAK,CAAC;AACnB,GAAG;AACH,EAAE,IAAI,EAAE;AACR,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,KAAK,CAAC;AACnB,GAAG;AACH,CAAC,EAAE;AACS,MAAC,UAAU,GAAG,UAAU,CAAC;AACrC,EAAE,GAAG,cAAc;AACnB,EAAE,MAAM,EAAE,OAAO;AACjB,CAAC,EAAE;AACS,MAAC,UAAU,GAAG;AAC1B,EAAE,CAAC,kBAAkB,GAAG,CAAC,GAAG,KAAK,QAAQ,CAAC,GAAG,CAAC,IAAI,QAAQ,CAAC,GAAG,CAAC,IAAI,SAAS,CAAC,GAAG,CAAC;AACjF,EAAE,CAAC,YAAY,GAAG,CAAC,GAAG,KAAK,QAAQ,CAAC,GAAG,CAAC,IAAI,QAAQ,CAAC,GAAG,CAAC,IAAI,SAAS,CAAC,GAAG,CAAC;AAC3E;;;;"}