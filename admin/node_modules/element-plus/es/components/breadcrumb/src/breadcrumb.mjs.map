{"version": 3, "file": "breadcrumb.mjs", "sources": ["../../../../../../packages/components/breadcrumb/src/breadcrumb.ts"], "sourcesContent": ["import { buildProps, iconPropType } from '@element-plus/utils'\nimport type { ExtractPropTypes } from 'vue'\n\nexport const breadcrumbProps = buildProps({\n  /**\n   * @description separator character\n   */\n  separator: {\n    type: String,\n    default: '/',\n  },\n  /**\n   * @description icon component of icon separator\n   */\n  separatorIcon: {\n    type: iconPropType,\n  },\n} as const)\nexport type BreadcrumbProps = ExtractPropTypes<typeof breadcrumbProps>\n"], "names": [], "mappings": ";;;AACY,MAAC,eAAe,GAAG,UAAU,CAAC;AAC1C,EAAE,SAAS,EAAE;AACb,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,GAAG;AAChB,GAAG;AACH,EAAE,aAAa,EAAE;AACjB,IAAI,IAAI,EAAE,YAAY;AACtB,GAAG;AACH,CAAC;;;;"}