{"version": 3, "file": "type.mjs", "sources": ["../../../../../../packages/components/splitter/src/type.ts"], "sourcesContent": ["import type { InjectionKey, UnwrapRef } from 'vue'\n\nexport type Layout = 'horizontal' | 'vertical'\n\nexport type PanelItemState = UnwrapRef<{\n  uid: number\n  el: HTMLElement\n  collapsible: { start?: boolean; end?: boolean }\n  max?: number | string\n  min?: number | string\n  resizable: boolean\n  size?: number | string\n  setIndex: (val: number) => void\n}>\n\nexport interface SplitterRootContext {\n  panels: PanelItemState[]\n  layout: Layout\n  containerSize: number\n  movingIndex: { index: number; confirmed: boolean } | null\n  percentSizes: number[]\n  pxSizes: number[]\n  registerPanel: (pane: PanelItemState) => void\n  sortPanel: (pane: PanelItemState) => void\n  unregisterPanel: (uid: number) => void\n  onCollapse: (index: number, type: 'start' | 'end') => void\n  onMoveEnd: () => void\n  onMoveStart: (index: number) => void\n  onMoving: (index: number, offset: number) => void\n}\n\nexport const splitterRootContextKey: InjectionKey<SplitterRootContext> = Symbol(\n  'splitterRootContextKey'\n)\n"], "names": [], "mappings": "AAAY,MAAC,sBAAsB,GAAG,MAAM,CAAC,wBAAwB;;;;"}