{"version": 3, "file": "row2.mjs", "sources": ["../../../../../../packages/components/row/src/row.vue"], "sourcesContent": ["<template>\n  <component :is=\"tag\" :class=\"rowKls\" :style=\"style\">\n    <slot />\n  </component>\n</template>\n\n<script setup lang=\"ts\">\nimport { computed, provide } from 'vue'\nimport { useNamespace } from '@element-plus/hooks'\nimport { rowContextKey } from './constants'\nimport { rowProps } from './row'\nimport type { CSSProperties } from 'vue'\n\ndefineOptions({\n  name: 'ElRow',\n})\n\nconst props = defineProps(rowProps)\n\nconst ns = useNamespace('row')\nconst gutter = computed(() => props.gutter)\n\nprovide(rowContextKey, {\n  gutter,\n})\n\nconst style = computed(() => {\n  const styles: CSSProperties = {}\n  if (!props.gutter) {\n    return styles\n  }\n\n  styles.marginRight = styles.marginLeft = `-${props.gutter / 2}px`\n  return styles\n})\n\nconst rowKls = computed(() => [\n  ns.b(),\n  ns.is(`justify-${props.justify}`, props.justify !== 'start'),\n  ns.is(`align-${props.align}`, !!props.align),\n])\n</script>\n"], "names": [], "mappings": ";;;;;;mCAac,CAAA;AAAA,EACZ,IAAM,EAAA,OAAA;AACR,CAAA,CAAA,CAAA;;;;;;AAIA,IAAM,MAAA,EAAA,GAAK,aAAa,KAAK,CAAA,CAAA;AAC7B,IAAA,MAAM,MAAS,GAAA,QAAA,CAAS,MAAM,KAAA,CAAM,MAAM,CAAA,CAAA;AAE1C,IAAA,OAAA,CAAQ,aAAe,EAAA;AAAA,MACrB,MAAA;AAAA,KACD,CAAA,CAAA;AAED,IAAM,MAAA,KAAA,GAAQ,SAAS,MAAM;AAC3B,MAAA,MAAM,SAAwB,EAAC,CAAA;AAC/B,MAAI,IAAA,CAAC,MAAM,MAAQ,EAAA;AACjB,QAAO,OAAA,MAAA,CAAA;AAAA,OACT;AAEA,MAAA,MAAA,CAAO,cAAc,MAAO,CAAA,UAAA,GAAa,CAAI,CAAA,EAAA,KAAA,CAAM,SAAS,CAAC,CAAA,EAAA,CAAA,CAAA;AAC7D,MAAO,OAAA,MAAA,CAAA;AAAA,KACR,CAAA,CAAA;AAED,IAAM,MAAA,MAAA,GAAS,SAAS,MAAM;AAAA,MAC5B,GAAG,CAAE,EAAA;AAAA,MACL,EAAA,CAAG,GAAG,CAAW,QAAA,EAAA,KAAA,CAAM,OAAO,CAAI,CAAA,EAAA,KAAA,CAAM,YAAY,OAAO,CAAA;AAAA,MAC3D,EAAA,CAAG,GAAG,CAAS,MAAA,EAAA,KAAA,CAAM,KAAK,CAAI,CAAA,EAAA,CAAC,CAAC,KAAA,CAAM,KAAK,CAAA;AAAA,KAC5C,CAAA,CAAA;;;;;;;;;;;;;;;;;;"}