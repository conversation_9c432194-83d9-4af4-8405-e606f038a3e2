{"version": 3, "file": "current.mjs", "sources": ["../../../../../../../packages/components/table/src/store/current.ts"], "sourcesContent": ["// @ts-nocheck\nimport { getCurrentInstance, ref, unref } from 'vue'\nimport { isNull } from 'lodash-unified'\nimport { getRowIdentity } from '../util'\n\nimport type { Ref } from 'vue'\nimport type { Table } from '../table/defaults'\nimport type { WatcherPropsData } from '.'\n\nfunction useCurrent<T>(watcherData: WatcherPropsData<T>) {\n  const instance = getCurrentInstance() as Table<T>\n  const _currentRowKey = ref<string>(null)\n  const currentRow: Ref<T> = ref(null)\n\n  const setCurrentRowKey = (key: string) => {\n    instance.store.assertRowKey()\n    _currentRowKey.value = key\n    setCurrentRowByKey(key)\n  }\n\n  const restoreCurrentRowKey = () => {\n    _currentRowKey.value = null\n  }\n\n  const setCurrentRowByKey = (key: string) => {\n    const { data, rowKey } = watcherData\n    let _currentRow = null\n    if (rowKey.value) {\n      _currentRow = (unref(data) || []).find(\n        (item) => getRowIdentity(item, rowKey.value) === key\n      )\n    }\n    currentRow.value = _currentRow\n    instance.emit('current-change', currentRow.value, null)\n  }\n\n  const updateCurrentRow = (_currentRow: T) => {\n    const oldCurrentRow = currentRow.value\n    if (_currentRow && _currentRow !== oldCurrentRow) {\n      currentRow.value = _currentRow\n      instance.emit('current-change', currentRow.value, oldCurrentRow)\n      return\n    }\n    if (!_currentRow && oldCurrentRow) {\n      currentRow.value = null\n      instance.emit('current-change', null, oldCurrentRow)\n    }\n  }\n\n  const updateCurrentRowData = () => {\n    const rowKey = watcherData.rowKey.value\n    // data 为 null 时，解构时的默认值会被忽略\n    const data = watcherData.data.value || []\n    const oldCurrentRow = currentRow.value\n    // 当 currentRow 不在 data 中时尝试更新数据\n    if (!data.includes(oldCurrentRow) && oldCurrentRow) {\n      if (rowKey) {\n        const currentRowKey = getRowIdentity(oldCurrentRow, rowKey)\n        setCurrentRowByKey(currentRowKey)\n      } else {\n        currentRow.value = null\n      }\n      if (isNull(currentRow.value)) {\n        instance.emit('current-change', null, oldCurrentRow)\n      }\n    } else if (_currentRowKey.value) {\n      // 把初始时下设置的 rowKey 转化成 rowData\n      setCurrentRowByKey(_currentRowKey.value)\n      restoreCurrentRowKey()\n    }\n  }\n\n  return {\n    setCurrentRowKey,\n    restoreCurrentRowKey,\n    setCurrentRowByKey,\n    updateCurrentRow,\n    updateCurrentRowData,\n    states: {\n      _currentRowKey,\n      currentRow,\n    },\n  }\n}\n\nexport default useCurrent\n"], "names": [], "mappings": ";;;;AAGA,SAAS,UAAU,CAAC,WAAW,EAAE;AACjC,EAAE,MAAM,QAAQ,GAAG,kBAAkB,EAAE,CAAC;AACxC,EAAE,MAAM,cAAc,GAAG,GAAG,CAAC,IAAI,CAAC,CAAC;AACnC,EAAE,MAAM,UAAU,GAAG,GAAG,CAAC,IAAI,CAAC,CAAC;AAC/B,EAAE,MAAM,gBAAgB,GAAG,CAAC,GAAG,KAAK;AACpC,IAAI,QAAQ,CAAC,KAAK,CAAC,YAAY,EAAE,CAAC;AAClC,IAAI,cAAc,CAAC,KAAK,GAAG,GAAG,CAAC;AAC/B,IAAI,kBAAkB,CAAC,GAAG,CAAC,CAAC;AAC5B,GAAG,CAAC;AACJ,EAAE,MAAM,oBAAoB,GAAG,MAAM;AACrC,IAAI,cAAc,CAAC,KAAK,GAAG,IAAI,CAAC;AAChC,GAAG,CAAC;AACJ,EAAE,MAAM,kBAAkB,GAAG,CAAC,GAAG,KAAK;AACtC,IAAI,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,WAAW,CAAC;AACzC,IAAI,IAAI,WAAW,GAAG,IAAI,CAAC;AAC3B,IAAI,IAAI,MAAM,CAAC,KAAK,EAAE;AACtB,MAAM,WAAW,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,EAAE,EAAE,IAAI,CAAC,CAAC,IAAI,KAAK,cAAc,CAAC,IAAI,EAAE,MAAM,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,CAAC;AACnG,KAAK;AACL,IAAI,UAAU,CAAC,KAAK,GAAG,WAAW,CAAC;AACnC,IAAI,QAAQ,CAAC,IAAI,CAAC,gBAAgB,EAAE,UAAU,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;AAC5D,GAAG,CAAC;AACJ,EAAE,MAAM,gBAAgB,GAAG,CAAC,WAAW,KAAK;AAC5C,IAAI,MAAM,aAAa,GAAG,UAAU,CAAC,KAAK,CAAC;AAC3C,IAAI,IAAI,WAAW,IAAI,WAAW,KAAK,aAAa,EAAE;AACtD,MAAM,UAAU,CAAC,KAAK,GAAG,WAAW,CAAC;AACrC,MAAM,QAAQ,CAAC,IAAI,CAAC,gBAAgB,EAAE,UAAU,CAAC,KAAK,EAAE,aAAa,CAAC,CAAC;AACvE,MAAM,OAAO;AACb,KAAK;AACL,IAAI,IAAI,CAAC,WAAW,IAAI,aAAa,EAAE;AACvC,MAAM,UAAU,CAAC,KAAK,GAAG,IAAI,CAAC;AAC9B,MAAM,QAAQ,CAAC,IAAI,CAAC,gBAAgB,EAAE,IAAI,EAAE,aAAa,CAAC,CAAC;AAC3D,KAAK;AACL,GAAG,CAAC;AACJ,EAAE,MAAM,oBAAoB,GAAG,MAAM;AACrC,IAAI,MAAM,MAAM,GAAG,WAAW,CAAC,MAAM,CAAC,KAAK,CAAC;AAC5C,IAAI,MAAM,IAAI,GAAG,WAAW,CAAC,IAAI,CAAC,KAAK,IAAI,EAAE,CAAC;AAC9C,IAAI,MAAM,aAAa,GAAG,UAAU,CAAC,KAAK,CAAC;AAC3C,IAAI,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,IAAI,aAAa,EAAE;AACxD,MAAM,IAAI,MAAM,EAAE;AAClB,QAAQ,MAAM,aAAa,GAAG,cAAc,CAAC,aAAa,EAAE,MAAM,CAAC,CAAC;AACpE,QAAQ,kBAAkB,CAAC,aAAa,CAAC,CAAC;AAC1C,OAAO,MAAM;AACb,QAAQ,UAAU,CAAC,KAAK,GAAG,IAAI,CAAC;AAChC,OAAO;AACP,MAAM,IAAI,MAAM,CAAC,UAAU,CAAC,KAAK,CAAC,EAAE;AACpC,QAAQ,QAAQ,CAAC,IAAI,CAAC,gBAAgB,EAAE,IAAI,EAAE,aAAa,CAAC,CAAC;AAC7D,OAAO;AACP,KAAK,MAAM,IAAI,cAAc,CAAC,KAAK,EAAE;AACrC,MAAM,kBAAkB,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;AAC/C,MAAM,oBAAoB,EAAE,CAAC;AAC7B,KAAK;AACL,GAAG,CAAC;AACJ,EAAE,OAAO;AACT,IAAI,gBAAgB;AACpB,IAAI,oBAAoB;AACxB,IAAI,kBAAkB;AACtB,IAAI,gBAAgB;AACpB,IAAI,oBAAoB;AACxB,IAAI,MAAM,EAAE;AACZ,MAAM,cAAc;AACpB,MAAM,UAAU;AAChB,KAAK;AACL,GAAG,CAAC;AACJ;;;;"}