{"version": 3, "file": "index.mjs", "sources": ["../../../../../packages/components/time-select/index.ts"], "sourcesContent": ["import { withInstall } from '@element-plus/utils'\nimport TimeSelect from './src/time-select.vue'\n\nimport type { SFCWithInstall } from '@element-plus/utils'\n\nexport const ElTimeSelect: SFCWithInstall<typeof TimeSelect> =\n  withInstall(TimeSelect)\nexport default ElTimeSelect\n\nexport * from './src/time-select'\n"], "names": [], "mappings": ";;;;AAEY,MAAC,YAAY,GAAG,WAAW,CAAC,UAAU;;;;"}