{"version": 3, "file": "bar2.mjs", "sources": ["../../../../../../packages/components/scrollbar/src/bar.vue"], "sourcesContent": ["<template>\n  <thumb :move=\"moveX\" :ratio=\"ratioX\" :size=\"sizeWidth\" :always=\"always\" />\n  <thumb\n    :move=\"moveY\"\n    :ratio=\"ratioY\"\n    :size=\"sizeHeight\"\n    vertical\n    :always=\"always\"\n  />\n</template>\n\n<script lang=\"ts\" setup>\nimport { inject, ref } from 'vue'\nimport { GAP } from './util'\nimport Thumb from './thumb.vue'\nimport { barProps } from './bar'\nimport { scrollbarContextKey } from './constants'\n\nconst props = defineProps(barProps)\n\nconst scrollbar = inject(scrollbarContextKey)\n\nconst moveX = ref(0)\nconst moveY = ref(0)\nconst sizeWidth = ref('')\nconst sizeHeight = ref('')\nconst ratioY = ref(1)\nconst ratioX = ref(1)\n\nconst handleScroll = (wrap: HTMLDivElement) => {\n  if (wrap) {\n    const offsetHeight = wrap.offsetHeight - GAP\n    const offsetWidth = wrap.offsetWidth - GAP\n\n    moveY.value = ((wrap.scrollTop * 100) / offsetHeight) * ratioY.value\n    moveX.value = ((wrap.scrollLeft * 100) / offsetWidth) * ratioX.value\n  }\n}\n\nconst update = () => {\n  const wrap = scrollbar?.wrapElement\n  if (!wrap) return\n  const offsetHeight = wrap.offsetHeight - GAP\n  const offsetWidth = wrap.offsetWidth - GAP\n\n  const originalHeight = offsetHeight ** 2 / wrap.scrollHeight\n  const originalWidth = offsetWidth ** 2 / wrap.scrollWidth\n  const height = Math.max(originalHeight, props.minSize)\n  const width = Math.max(originalWidth, props.minSize)\n\n  ratioY.value =\n    originalHeight /\n    (offsetHeight - originalHeight) /\n    (height / (offsetHeight - height))\n  ratioX.value =\n    originalWidth /\n    (offsetWidth - originalWidth) /\n    (width / (offsetWidth - width))\n\n  sizeHeight.value = height + GAP < offsetHeight ? `${height}px` : ''\n  sizeWidth.value = width + GAP < offsetWidth ? `${width}px` : ''\n}\n\ndefineExpose({\n  handleScroll,\n  update,\n})\n</script>\n"], "names": [], "mappings": ";;;;;;;;;;;;AAoBA,IAAM,MAAA,SAAA,GAAY,OAAO,mBAAmB,CAAA,CAAA;AAE5C,IAAM,MAAA,KAAA,GAAQ,IAAI,CAAC,CAAA,CAAA;AACnB,IAAM,MAAA,KAAA,GAAQ,IAAI,CAAC,CAAA,CAAA;AACnB,IAAM,MAAA,SAAA,GAAY,IAAI,EAAE,CAAA,CAAA;AACxB,IAAM,MAAA,UAAA,GAAa,IAAI,EAAE,CAAA,CAAA;AACzB,IAAM,MAAA,MAAA,GAAS,IAAI,CAAC,CAAA,CAAA;AACpB,IAAM,MAAA,MAAA,GAAS,IAAI,CAAC,CAAA,CAAA;AAEpB,IAAM,MAAA,YAAA,GAAe,CAAC,IAAyB,KAAA;AAC7C,MAAA,IAAI,IAAM,EAAA;AACR,QAAM,MAAA,YAAA,GAAe,KAAK,YAAe,GAAA,GAAA,CAAA;AACzC,QAAM,MAAA,WAAA,GAAc,KAAK,WAAc,GAAA,GAAA,CAAA;AAEvC,QAAA,KAAA,CAAM,KAAU,GAAA,IAAA,CAAK,SAAY,GAAA,GAAA,GAAO,eAAgB,MAAO,CAAA,KAAA,CAAA;AAC/D,QAAA,KAAA,CAAM,KAAU,GAAA,IAAA,CAAK,UAAa,GAAA,GAAA,GAAO,cAAe,MAAO,CAAA,KAAA,CAAA;AAAA,OACjE;AAAA,KACF,CAAA;AAEA,IAAA,MAAM,SAAS,MAAM;AACnB,MAAA,MAAM,OAAO,SAAW,IAAA,IAAA,GAAA,KAAA,CAAA,GAAA,SAAA,CAAA,WAAA,CAAA;AACxB,MAAA,IAAI,CAAC,IAAM;AACX,QAAM,OAAA;AACN,MAAM,MAAA,YAAA,OAAmB,CAAc,YAAA,GAAA,GAAA,CAAA;AAEvC,MAAM,MAAA,WAAA,GAAA,IAAiC,CAAA,WAAA,GAAA,GAAA,CAAI;AAC3C,MAAM,MAAA,cAAA,GAA+B,YAAA,IAAA,CAAA,GAAS,IAAA,CAAA,YAAA,CAAA;AAC9C,MAAA,MAAM,aAAS,GAAS,WAAA,IAAA,CAAA,QAAsB,WAAO,CAAA;AACrD,MAAA,MAAM,MAAQ,GAAA,IAAK,CAAI,GAAA,CAAA,cAAe,OAAa,CAAA,OAAA,CAAA,CAAA;AAEnD,MAAA,MAAA,KACE,GAAA,IAAA,CAAA,GAAA,CAAA,aACgB,EAAA,KAAA,CAAA,OAAA,CAAA,CAAA;AAElB,MAAA,MAAA,CAAO,KACL,GAAA,cAAA,IACe,YAAA,GAAA,cAAA,CAAA,UACL,IAAc,YAAA,GAAA,MAAA,CAAA,CAAA,CAAA;AAE1B,MAAA,MAAA,CAAA,qBAA4B,IAAA,WAAqB,GAAA,aAAgB,CAAA,IAAA,KAAA,IAAA,WAAA,GAAA,KAAA,CAAA,CAAA,CAAA;AACjE,MAAA,UAAU,SAAgB,MAAA,GAAA,GAAA,GAAoB,YAAA,GAAG,SAAY,CAAA,EAAA,CAAA,GAAA,EAAA,CAAA;AAAA,MAC/D,SAAA,CAAA,KAAA,GAAA,KAAA,GAAA,GAAA,GAAA,WAAA,GAAA,CAAA,EAAA,KAAA,CAAA,EAAA,CAAA,GAAA,EAAA,CAAA;AAEA,KAAa,CAAA;AAAA,IACX,MAAA,CAAA;AAAA,MACA,YAAA;AAAA,MACD,MAAA;;;;;;;;;;;;;;;;;;;;;;;;;"}