{"version": 3, "file": "alpha-slider.mjs", "sources": ["../../../../../../../packages/components/color-picker/src/props/alpha-slider.ts"], "sourcesContent": ["import { buildProps, definePropType } from '@element-plus/utils'\n\nimport type { ExtractPropTypes } from 'vue'\nimport type Color from '../utils/color'\n\nexport const alphaSliderProps = buildProps({\n  color: {\n    type: definePropType<Color>(Object),\n    required: true,\n  },\n  vertical: {\n    type: Boolean,\n    default: false,\n  },\n} as const)\n\nexport type AlphaSliderProps = ExtractPropTypes<typeof alphaSliderProps>\n"], "names": [], "mappings": ";;AACY,MAAC,gBAAgB,GAAG,UAAU,CAAC;AAC3C,EAAE,KAAK,EAAE;AACT,IAAI,IAAI,EAAE,cAAc,CAAC,MAAM,CAAC;AAChC,IAAI,QAAQ,EAAE,IAAI;AAClB,GAAG;AACH,EAAE,QAAQ,EAAE;AACZ,IAAI,IAAI,EAAE,OAAO;AACjB,IAAI,OAAO,EAAE,KAAK;AAClB,GAAG;AACH,CAAC;;;;"}