{"version": 3, "file": "arrow2.mjs", "sources": ["../../../../../../packages/components/popper/src/arrow.vue"], "sourcesContent": ["<template>\n  <span\n    ref=\"arrowRef\"\n    :class=\"ns.e('arrow')\"\n    :style=\"arrowStyle\"\n    data-popper-arrow\n  />\n</template>\n\n<script lang=\"ts\" setup>\nimport { inject, onBeforeUnmount } from 'vue'\nimport { useNamespace } from '@element-plus/hooks'\nimport { POPPER_CONTENT_INJECTION_KEY } from './constants'\n\ndefineOptions({\n  name: 'ElPopperArrow',\n  inheritAttrs: false,\n})\n\nconst ns = useNamespace('popper')\nconst { arrowRef, arrowStyle } = inject(\n  POPPER_CONTENT_INJECTION_KEY,\n  undefined\n)!\n\nonBeforeUnmount(() => {\n  arrowRef.value = undefined\n})\n\ndefineExpose({\n  /**\n   * @description Arrow element\n   */\n  arrowRef,\n})\n</script>\n"], "names": ["_openBlock", "_createElementBlock", "_normalizeClass", "_unref", "_normalizeStyle"], "mappings": ";;;;;mCAcc,CAAA;AAAA,EACZ,IAAM,EAAA,eAAA;AAAA,EACN,YAAc,EAAA,KAAA;AAChB,CAAA,CAAA,CAAA;;;;AAEA,IAAM,MAAA,EAAA,GAAK,aAAa,QAAQ,CAAA,CAAA;AAChC,IAAM,MAAA,EAAE,QAAU,EAAA,UAAA,EAAe,GAAA,MAAA,CAAA,4BAAA,EAAA,KAAA,CAAA,CAAA,CAAA;AAAA,IAC/B,eAAA,CAAA,MAAA;AAAA,MACA,QAAA,CAAA,KAAA,GAAA,KAAA,CAAA,CAAA;AAAA,KACF,CAAA,CAAA;AAEA,IAAA,MAAA,CAAA;AACE,MAAA,QAAA;AAAiB,KAClB,CAAA,CAAA;AAED,IAAa,OAAA,CAAA,IAAA,EAAA,MAAA,KAAA;AAAA,MAAA,OAAAA,SAAA,EAAA,EAAAC,kBAAA,CAAA,MAAA,EAAA;AAAA,QAAA,OAAA,EAAA,UAAA;AAAA,QAAA,GAAA,EAAA,QAAA;AAAA,QAIX,KAAA,EAAAC,cAAA,CAAAC,KAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,OAAA,CAAA,CAAA;AAAA,QACD,KAAA,EAAAC,cAAA,CAAAD,KAAA,CAAA,UAAA,CAAA,CAAA;;;;;;;;;;"}