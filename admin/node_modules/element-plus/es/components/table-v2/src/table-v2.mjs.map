{"version": 3, "file": "table-v2.mjs", "sources": ["../../../../../../packages/components/table-v2/src/table-v2.tsx"], "sourcesContent": ["import { defineComponent, provide, unref } from 'vue'\nimport { useNamespace } from '@element-plus/hooks'\nimport { useTable } from './use-table'\nimport { TableV2InjectionKey } from './tokens'\nimport { tableV2Props } from './table'\n// renderers\nimport MainTable from './renderers/main-table'\nimport LeftTable from './renderers/left-table'\nimport RightTable from './renderers/right-table'\nimport Row from './renderers/row'\nimport Cell from './renderers/cell'\nimport Header from './renderers/header'\nimport HeaderCell from './renderers/header-cell'\nimport Footer from './renderers/footer'\nimport Empty from './renderers/empty'\nimport Overlay from './renderers/overlay'\n\nimport type { CSSProperties } from 'vue'\nimport type { TableGridRowSlotParams } from './table-grid'\nimport type { ScrollStrategy } from './composables/use-scrollbar'\nimport type {\n  TableV2HeaderRendererParams,\n  TableV2HeaderRowCellRendererParams,\n  TableV2RowCellRenderParam,\n} from './components'\nimport type { KeyType } from './types'\n\nconst COMPONENT_NAME = 'ElTableV2'\n\nconst TableV2 = defineComponent({\n  name: COMPONENT_NAME,\n  props: tableV2Props,\n  setup(props, { slots, expose }) {\n    const ns = useNamespace('table-v2')\n\n    const {\n      columnsStyles,\n      fixedColumnsOnLeft,\n      fixedColumnsOnRight,\n      mainColumns,\n      mainTableHeight,\n      fixedTableHeight,\n      leftTableWidth,\n      rightTableWidth,\n      data,\n      depthMap,\n      expandedRowKeys,\n      hasFixedColumns,\n      mainTableRef,\n      leftTableRef,\n      rightTableRef,\n      isDynamic,\n      isResetting,\n      isScrolling,\n\n      bodyWidth,\n      emptyStyle,\n      rootStyle,\n      footerHeight,\n\n      showEmpty,\n\n      // exposes\n      scrollTo,\n      scrollToLeft,\n      scrollToTop,\n      scrollToRow,\n\n      getRowHeight,\n      onColumnSorted,\n      onRowHeightChange,\n      onRowHovered,\n      onRowExpanded,\n      onRowsRendered,\n      onScroll,\n      onVerticalScroll,\n    } = useTable(props)\n\n    expose({\n      /**\n       * @description scroll to a given position\n       * @params params {{ scrollLeft?: number, scrollTop?: number }} where to scroll to.\n       */\n      scrollTo,\n      /**\n       * @description scroll to a given position horizontally\n       * @params scrollLeft {Number} where to scroll to.\n       */\n      scrollToLeft,\n      /**\n       * @description scroll to a given position vertically\n       * @params scrollTop { Number } where to scroll to.\n       */\n      scrollToTop,\n      /**\n       * @description scroll to a given row\n       * @params row {Number} which row to scroll to\n       * @params @optional strategy {ScrollStrategy} use what strategy to scroll to\n       */\n      scrollToRow,\n    })\n\n    provide(TableV2InjectionKey, {\n      ns,\n      isResetting,\n      isScrolling,\n    })\n\n    return () => {\n      const {\n        cache,\n        cellProps,\n        estimatedRowHeight,\n        expandColumnKey,\n        fixedData,\n        headerHeight,\n        headerClass,\n        headerProps,\n        headerCellProps,\n        sortBy,\n        sortState,\n        rowHeight,\n        rowClass,\n        rowEventHandlers,\n        rowKey,\n        rowProps,\n        scrollbarAlwaysOn,\n        indentSize,\n        iconSize,\n        useIsScrolling,\n        vScrollbarSize,\n        width,\n      } = props\n\n      const _data = unref(data)\n\n      const mainTableProps = {\n        cache,\n        class: ns.e('main'),\n        columns: unref(mainColumns),\n        data: _data,\n        fixedData,\n        estimatedRowHeight,\n        bodyWidth: unref(bodyWidth),\n        headerHeight,\n        headerWidth: unref(bodyWidth),\n        height: unref(mainTableHeight),\n        mainTableRef,\n        rowKey,\n        rowHeight,\n        scrollbarAlwaysOn,\n        scrollbarStartGap: 2,\n        scrollbarEndGap: vScrollbarSize,\n        useIsScrolling,\n        width,\n        getRowHeight,\n        onRowsRendered,\n        onScroll,\n      }\n\n      const leftColumnsWidth = unref(leftTableWidth)\n      const _fixedTableHeight = unref(fixedTableHeight)\n\n      const leftTableProps = {\n        cache,\n        class: ns.e('left'),\n        columns: unref(fixedColumnsOnLeft),\n        data: _data,\n        fixedData,\n        estimatedRowHeight,\n        leftTableRef,\n        rowHeight,\n        bodyWidth: leftColumnsWidth,\n        headerWidth: leftColumnsWidth,\n        headerHeight,\n        height: _fixedTableHeight,\n        rowKey,\n        scrollbarAlwaysOn,\n        scrollbarStartGap: 2,\n        scrollbarEndGap: vScrollbarSize,\n        useIsScrolling,\n        width: leftColumnsWidth,\n        getRowHeight,\n        onScroll: onVerticalScroll,\n      }\n\n      const rightColumnsWidth = unref(rightTableWidth)\n\n      const rightTableProps = {\n        cache,\n        class: ns.e('right'),\n        columns: unref(fixedColumnsOnRight),\n        data: _data,\n        fixedData,\n        estimatedRowHeight,\n        rightTableRef,\n        rowHeight,\n        bodyWidth: rightColumnsWidth,\n        headerWidth: rightColumnsWidth,\n        headerHeight,\n        height: _fixedTableHeight,\n        rowKey,\n        scrollbarAlwaysOn,\n        scrollbarStartGap: 2,\n        scrollbarEndGap: vScrollbarSize,\n        width: rightColumnsWidth,\n        style: `--${unref(\n          ns.namespace\n        )}-table-scrollbar-size: ${vScrollbarSize}px` as unknown as CSSProperties,\n        useIsScrolling,\n        getRowHeight,\n        onScroll: onVerticalScroll,\n      }\n      const _columnsStyles = unref(columnsStyles)\n\n      const tableRowProps = {\n        ns,\n        depthMap: unref(depthMap),\n        columnsStyles: _columnsStyles,\n        expandColumnKey,\n        expandedRowKeys: unref(expandedRowKeys),\n        estimatedRowHeight,\n        hasFixedColumns: unref(hasFixedColumns),\n        rowProps,\n        rowClass,\n        rowKey,\n        rowEventHandlers,\n        onRowHovered,\n        onRowExpanded,\n        onRowHeightChange,\n      }\n\n      const tableCellProps = {\n        cellProps,\n        expandColumnKey,\n        indentSize,\n        iconSize,\n        rowKey,\n        expandedRowKeys: unref(expandedRowKeys),\n        ns,\n      }\n\n      const tableHeaderProps = {\n        ns,\n        headerClass,\n        headerProps,\n        columnsStyles: _columnsStyles,\n      }\n\n      const tableHeaderCellProps = {\n        ns,\n\n        sortBy,\n        sortState,\n        headerCellProps,\n        onColumnSorted,\n      }\n\n      const tableSlots = {\n        row: (props: TableGridRowSlotParams) => (\n          <Row {...props} {...tableRowProps}>\n            {{\n              row: slots.row,\n              cell: (props: TableV2RowCellRenderParam) =>\n                slots.cell ? (\n                  <Cell\n                    {...props}\n                    {...tableCellProps}\n                    style={_columnsStyles[props.column.key as KeyType]}\n                  >\n                    {slots.cell(props)}\n                  </Cell>\n                ) : (\n                  <Cell\n                    {...props}\n                    {...tableCellProps}\n                    style={_columnsStyles[props.column.key as KeyType]}\n                  />\n                ),\n            }}\n          </Row>\n        ),\n        header: (props: TableV2HeaderRendererParams) => (\n          <Header {...props} {...tableHeaderProps}>\n            {{\n              header: slots.header,\n              cell: (props: TableV2HeaderRowCellRendererParams) =>\n                slots['header-cell'] ? (\n                  <HeaderCell\n                    {...props}\n                    {...tableHeaderCellProps}\n                    style={_columnsStyles[props.column.key as KeyType]}\n                  >\n                    {slots['header-cell'](props)}\n                  </HeaderCell>\n                ) : (\n                  <HeaderCell\n                    {...props}\n                    {...tableHeaderCellProps}\n                    style={_columnsStyles[props.column.key as KeyType]}\n                  />\n                ),\n            }}\n          </Header>\n        ),\n      }\n\n      const rootKls = [\n        props.class,\n        ns.b(),\n        ns.e('root'),\n        {\n          [ns.is('dynamic')]: unref(isDynamic),\n        },\n      ]\n\n      const footerProps = {\n        class: ns.e('footer'),\n        style: unref(footerHeight),\n      }\n\n      return (\n        <div class={rootKls} style={unref(rootStyle)}>\n          <MainTable {...mainTableProps}>{tableSlots}</MainTable>\n          <LeftTable {...leftTableProps}>{tableSlots}</LeftTable>\n          <RightTable {...rightTableProps}>{tableSlots}</RightTable>\n          {slots.footer && (\n            <Footer {...footerProps}>{{ default: slots.footer }}</Footer>\n          )}\n          {unref(showEmpty) && (\n            <Empty class={ns.e('empty')} style={unref(emptyStyle)}>\n              {{ default: slots.empty }}\n            </Empty>\n          )}\n          {slots.overlay && (\n            <Overlay class={ns.e('overlay')}>\n              {{ default: slots.overlay }}\n            </Overlay>\n          )}\n        </div>\n      )\n    }\n  },\n})\n\nexport default TableV2\n\nexport type TableV2Instance = InstanceType<typeof TableV2> & {\n  /**\n   * @description scroll to a given position\n   * @params params {{ scrollLeft?: number, scrollTop?: number }} where to scroll to.\n   */\n  scrollTo: (param: { scrollLeft?: number; scrollTop?: number }) => void\n  /**\n   * @description scroll to a given position horizontally\n   * @params scrollLeft {Number} where to scroll to.\n   */\n  scrollToLeft: (scrollLeft: number) => void\n  /**\n   * @description scroll to a given position vertically\n   * @params scrollTop { Number } where to scroll to.\n   */\n  scrollToTop: (scrollTop: number) => void\n  /**\n   * @description scroll to a given row\n   * @params row {Number} which row to scroll to\n   * @params strategy {ScrollStrategy} use what strategy to scroll to\n   */\n  scrollToRow(row: number, strategy?: ScrollStrategy): void\n}\n"], "names": ["Overlay", "COMPONENT_NAME", "TableV2", "defineComponent", "name", "props", "slots", "expose", "columnsStyles", "fixedColumnsOnLeft", "fixedColumnsOnRight", "mainColumns", "mainTableHeight", "fixedTableHeight", "leftTableWidth", "rightTableWidth", "data", "depthMap", "expandedRowKeys", "hasFixedColumns", "mainTableRef", "leftTableRef", "rightTableRef", "isDynamic", "isResetting", "isScrolling", "bodyWidth", "emptyStyle", "rootStyle", "footerHeight", "showEmpty", "scrollTo", "scrollToLeft", "scrollToTop", "scrollToRow", "getRowHeight", "onColumnSorted", "onRowHeightChange", "onRowsRendered", "onScroll", "onVerticalScroll", "useTable", "cache", "cellProps", "estimatedRowHeight", "expandColumnKey", "fixedData", "headerHeight", "headerClass", "headerProps", "headerCellProps", "sortBy", "sortState", "rowHeight", "rowClass", "rowEventHandlers", "<PERSON><PERSON><PERSON>", "rowProps", "scrollbarAlwaysOn", "indentSize", "iconSize", "useIsScrolling", "vScrollbarSize", "width", "_data", "class", "columns", "unref", "headerWidth", "height", "scrollbarStartGap", "scrollbarEndGap", "_fixedTableHeight", "style", "tableRowProps", "ns", "_createVNode", "_mergeProps", "tableHeaderProps", "_columnsStyles", "tableSlots", "row", "cell", "column", "header"], "mappings": ";;;;;;;;;;;;;;;;AAeA,SAAOA,WAAa;;;;;;AAYpB,EAAMC,KAAAA,EAAAA;AAEN,EAAMC,KAAAA,CAAAA,KAAO,EAAGC;AACdC,IAAAA,KAD8B;AAE9BC,IAAAA,MAF8B;;IAGzB,WAAQ,YAAA,CAAA,UAAA,CAAA,CAAA;IAAEC,MAAF;AAASC,MAAAA,aAAAA;AAAT,MAAmB,kBAAA;AAC9B,MAAA,mBAAuB;MAEjB,WAAA;MACJC,eADI;MAEJC,gBAFI;MAGJC,cAHI;MAIJC,eAJI;MAKJC,IALI;MAMJC,QANI;MAOJC,eAPI;MAQJC,eARI;MASJC,YATI;MAUJC,YAVI;MAWJC,aAXI;MAYJC,SAZI;MAaJC,WAbI;MAcJC,WAdI;MAeJC,SAfI;MAgBJC,UAhBI;MAiBJC,SAjBI;MAkBJC,YAlBI;MAoBJC,SApBI;MAqBJC,QArBI;MAsBJC,YAtBI;MAuBJC,WAvBI;MAyBJC,WAzBI;AA2BJ,MAAA,YAAA;MACAC,cA5BI;MA6BJC,iBA7BI;MA8BJC,YA9BI;MA+BJC,aA/BI;MAiCJC,cAjCI;MAkCJC,QAlCI;MAmCJC,gBAnCI;QAAA,QAAA,CAAA,KAAA,CAAA,CAAA;UAAA,CAAA;MAsCJC,QAtCI;MAuCJC,YAvCI;AAwCJC,MAAAA,WAAAA;MACEC,WAASpC;AAEbE,KAAAA,CAAAA,CAAAA;AACE,IAAA,OAAA,CAAA,mBAAA,EAAA;AACN,MAAA,EAAA;AACA,MAAA,WAAA;AACA,MAAA,WAAA;MACMwB,CALK;;AAML,MAAA,MAAA;AACN,QAAA,KAAA;AACA,QAAA,SAAA;AACA,QAAA,kBAAA;QATW,eAAA;;AAWL,QAAA,YAAA;AACN,QAAA,WAAA;AACA,QAAA,WAAA;AACA,QAAA,eAAA;QAdW,MAAA;;AAgBL,QAAA,SAAA;AACN,QAAA,QAAA;AACA,QAAA,gBAAA;AACA,QAAA,MAAA;AACA,QAAA,QAAA;AACMG,QAAAA,iBAAAA;AArBK,QAAP,UAAA;QAwBO;QAAsB,cAAA;QAAA,cAAA;AAG3BT,QAAAA,KAAAA;AAH2B,OAA7B,GAAA,KAAA,CAAA;AAMA,MAAA,MAAa,KAAA,GAAA,KAAA,CAAA,IAAA,CAAA,CAAA;MACX,MAAM,cAAA,GAAA;QACJiB,KADI;QAEJC,KAFI,EAAA,EAAA,CAAA,CAAA,CAAA,MAAA,CAAA;QAGJC,OAHI,EAAA,KAAA,CAAA,WAAA,CAAA;QAIJC,IAJI,EAAA,KAAA;QAKJC,SALI;QAMJC,kBANI;QAOJC,SAPI,EAAA,KAAA,CAAA,SAAA,CAAA;QAQJC,YARI;QASJC,WATI,EAAA,KAAA,CAAA,SAAA,CAAA;QAUJC,MAVI,EAAA,KAAA,CAAA,eAAA,CAAA;QAWJC,YAXI;QAYJC,MAZI;QAaJC,SAbI;QAcJC,iBAdI;QAeJC,iBAfI,EAAA,CAAA;QAgBJC,eAhBI,EAAA,cAAA;QAiBJC,cAjBI;QAkBJC,KAlBI;QAmBJC,YAnBI;QAoBJC,cApBI;QAqBJC,QArBI;AAsBJC,OAAAA,CAAAA;AAtBI,MAAA,MAAN,gBAAA,GAAA,KAAA,CAAA,cAAA,CAAA,CAAA;;AAyBA,MAAA,MAAMC,cAAchD;;AAEpB,QAAA,kBAAoB,CAAG;QACrB0B,OADqB,EAAA,KAAA,CAAA,kBAAA,CAAA;AAErBuB,QAAAA,IAAAA,OAAO;AACPC,QAAAA,SAASC;AACTnD,QAAAA,kBAJqB;QAKrB8B,YALqB;QAMrBF,SANqB;AAOrBlB,QAAAA,SAAS,EAAEyC,gBAPU;QAQrBpB,WARqB,EAAA,gBAAA;AASrBqB,QAAAA,YAAaD;AACbE,QAAAA,MAAM,EAAEF,iBAAMvD;QACdQ,MAXqB;QAYrBoC,iBAZqB;QAarBH,iBAbqB,EAAA,CAAA;QAcrBK,eAdqB,EAAA,cAAA;AAerBY,QAAAA,cAAAA;AACAC,QAAAA,KAAAA,EAAAA,gBAhBqB;QAiBrBV,YAjBqB;QAkBrBE,QAlBqB,EAAA,gBAAA;QAmBrB5B;YAnBqB,iBAAA,GAAA,KAAA,CAAA,eAAA,CAAA,CAAA;AAqBrBI,MAAAA,MAAAA,eAAAA,GAAAA;QArBF,KAAA;AAwBA,QAAA,KAAsB,EAAA,EAAA,CAAA,CAAA,CAAA,OAAA,CAAA;;AACtB,QAAA,IAAMiC,EAAiB,KAAA;;AAEvB,QAAA,kBAAoB;QAClB9B,aADqB;AAErBuB,QAAAA,SAAS;AACTC,QAAAA,SAASC,EAAAA,iBAAM1D;AACfO,QAAAA,WAJqB,EAAA,iBAAA;QAKrB8B,YALqB;QAMrBF,MANqB,EAAA,iBAAA;QAOrBvB,MAPqB;QAQrBgC,iBARqB;AASrB3B,QAAAA,iBATqB,EAAA,CAAA;AAUrB0C,QAAAA,eAVqB,EAAA,cAAA;QAWrBrB,KAXqB,EAAA,iBAAA;AAYrBsB,QAAAA,KAAAA,GAAQG,EAZa,EAAA,KAAA,CAAA,EAAA,CAAA,SAAA,CAAA,CAAA,uBAAA,EAAA,cAAA,CAAA,EAAA,CAAA;QAarBhB,cAbqB;QAcrBE,YAdqB;AAerBY,QAAAA,QAAAA,EAAAA,gBAfqB;AAgBrBC,OAAAA,CAAAA;YAhBqB,cAAA,GAAA,KAAA,CAAA,aAAA,CAAA,CAAA;AAkBrBR,MAAAA,MAAAA,aAlBqB,GAAA;QAmBrB5B,EAnBqB;AAoBrBI,QAAAA,QAAQ,EAAEC,KAAAA,CAAAA,QAAAA,CAAAA;QApBZ,aAAA,EAAA,cAAA;AAuBA,QAAA,eAAuB;AAEvB,QAAA,sBAAwB,CAAA,eAAA,CAAA;QACtBE,kBADsB;AAEtBuB,QAAAA,sBAFsB,CAAA,eAAA,CAAA;AAGtBC,QAAAA,QAASC;AACTnD,QAAAA,QAJsB;QAKtB8B,MALsB;QAMtBF,gBANsB;QAOtBtB,YAPsB;QAQtB+B,aARsB;AAStB3B,QAAAA,iBATsB;AAUtB0C,OAAAA,CAAAA;YAVsB,cAAA,GAAA;AAYtBC,QAAAA,SAZsB;QAatBb,eAbsB;QActBE,UAdsB;AAetBY,QAAAA,QAAAA;AACAC,QAAAA,MAAAA;AACAR,QAAAA,eAjBsB,EAAA,KAAA,CAAA,eAAA,CAAA;QAkBtBU,EAAK;QAGLZ;YArBsB,gBAAA,GAAA;AAuBtBtB,QAAAA,EAAAA;QAvBF,WAAA;;AAyBA,QAAA,aAAoB,EAAA;;AAEpB,MAAA,MAAMmC,oBAAgB,GAAA;QACpBC,EADoB;AAEpB1D,QAAAA,MAAAA;AACAT,QAAAA,SAAAA;QACAqC,eAJoB;AAKpB3B,QAAAA,cAAAA;QACA0B;AACAzB,MAAAA,MAAAA,UAAAA,GAAiBgD;QACjBV,GARoB,EAAA,CAAA,MAAA,KAAAmB,WAAA,CAAA,GAAA,EAAAC,UAAA,CAAA,MAAA,EAAA,aAAA,CAAA,EAAA;UAAA,GAAA,EAAA,KAAA,CAAA,GAAA;UAAA,IAAA,EAAA,CAAA,MAAA,KAAA;YAAA,IAAA,KAAA,CAAA;YAAA,OAAA,KAAA,CAAA,IAAA,GAAAD,WAAA,CAAA,IAAA,EAAAC,UAAA,CAAA,MAAA,EAAA,cAAA,EAAA;cAAA,OAAA,EAAA,cAAA,CAAA,MAAA,CAAA,MAAA,CAAA,GAAA,CAAA;AAcpBxC,aAAAA,CAAAA,EAAAA,OAAAA,CAAAA,KAAAA,GAAAA,KAAAA,CAAAA,IAAAA,CAAAA,MAAAA,CAAAA,CAAAA,GAAAA,KAAAA,GAAAA;cAdF,OAAA,EAAA,MAAA,CAAA,KAAA,CAAA;AAiBA,4BAAuB,CAAA,IAAA,EAAAwC,UAAA,CAAA,MAAA,EAAA,cAAA,EAAA;cAAA,OAAA,EAAA,cAAA,CAAA,MAAA,CAAA,MAAA,CAAA,GAAA,CAAA;aAAA,CAAA,EAAA,IAAA,CAAA,CAAA;WAAA;SAAA,CAAA;QAKrBrB,MALqB,EAAA,CAAA,MAAA,KAAAoB,WAAA,CAAA,MAAA,EAAAC,UAAA,CAAA,MAAA,EAAA,gBAAA,CAAA,EAAA;AAMrB3D,UAAAA,MAAAA,EAAAA,KAAe,CAAEiD,MAAK;AACtBQ,UAAAA,IAAAA,EAAAA,CAAAA,MAAAA,KAAAA;YAPF,IAAA,MAAA,CAAA;AAUA,YAAMG,0BAAmB,CAAA,GAAAF,WAAA,CAAA,UAAA,EAAAC,UAAA,CAAA,MAAA,EAAA,oBAAA,EAAA;cAAA,OAAA,EAAA,cAAA,CAAA,MAAA,CAAA,MAAA,CAAA,GAAA,CAAA;aAAA,CAAA,EAAA,OAAA,CAAA,MAAA,GAAA,KAAA,CAAA,aAAA,CAAA,CAAA,MAAA,CAAA,CAAA,GAAA,MAAA,GAAA;cAAA,OAAA,EAAA,MAAA,CAAA,MAAA,CAAA;AAIvBrE,aAAAA,CAAAA,GAAAA,WAAeuE,CAAAA,UAAAA,EAAAA,UAAAA,CAAAA,MAAAA,EAAAA,oBAAAA,EAAAA;cAJjB,OAAA,EAAA,cAAA,CAAA,MAAA,CAAA,MAAA,CAAA,GAAA,CAAA;AAOA;WAA6B;SAAA,CAAA;QAI3B3B;YAJ2B,OAAA,GAAA,CAAA,KAAA,CAAA,KAAA,EAAA,EAAA,CAAA,CAAA,EAAA,EAAA,EAAA,CAAA,CAAA,CAAA,MAAA,CAAA,EAAA;AAM3BhB,QAAAA,CAAAA,EAAAA,CAAAA,EAAAA,CAAAA,SAAAA,CAAAA,GAAAA,KAAAA,CAAAA,SAAAA,CAAAA;OANF,CAAA,CAAA;AASA,MAAA,MAAM4C,WAAa,GAAA;AACjBC,QAAAA,KAAM5E,EAAAA,EAAAA,CAAD,CACMA,CAAAA,QAAAA,CAAAA;aAEF,EAAEC,KAAK,CAAC2E,YAHZ,CAAA;AAICC,OAAAA,CAAAA;AAAM,MAAA,OAAAN,WAAA,CAAA,KAAA,EAAA;;AAAA,QAAA,OAAA,EAAA,eAGIvE,CAAAA;AAFR,OAAA,EAAA,CAAAuE,WAAA,CAIWG,yBAAqBI,EAAN,OAAD,CAAA,UAAA,CAAA,GAAA,UAAA,GAAA;AAJzB,QAAA,OAAA,EAAA,MAAA,CAAA,UAMU,CAAA;AANV,OAAA,CAAA,EAAAP,WAAA,CAAA,SAAA,EAAA,cAAA,EAAA,OAAA,CAAA,UAAA,CAAA,GAAA,UAAA,GAAA;eAUQvE,EAAAA,MAAAA,CAAAA,UAAAA,CAAAA;AAVR,OAAA,CAAA,EAAAuE,WAAA,CAYWG,2BAAe,EAAD,OAAA,CAAA,UAAA,CAAA,GAAA,UAAA,GAAA;eAbrB,EAAA,MAAA,CAAA,UAAA,CAAA;AAAA,OAAA,CAAA,EAAA,KAAA,CAAA,MAAA,IAAAH,WAAA,CAAA,MAAA,EAAA,WAAA,EAAA;QAJP,OADY,EAAA,KAAA,CAAA,MAAA;AAwBjBQ,OAAAA,CAAAA,EAAAA,KAAS/E,CAAAA,SACKA,CAAAA,IAAAA,WAAAA,CAAAA,KAAAA,EAAAA;eAEF,EAAA,EAAO,CAAA,CAAA,CAAA,OAHX,CAAA;AAIF6E,QAAAA,SAAM,KAAA,CAAA,UAAA,CAAA;AAAA,OAAA,EAAA;;AAAA,OAAA,CAAA,EAAA,KAAA,CAAA,WACEN,WAAN,CAEQvE,OAAAA,EAAAA;AAFR,QAAA,OAAA,EAAA,EAAA,CAAA,CAAA,CAAA;AAAA,OAAA,EAAA;AAAA,QAAA,OAAA,EAAA,KAAA,CAAA,OAAA;;AAAA,KAAA,CAAA;;AADI,CAAA,CAAA,CAAA;AAJJ,gBAAA,OAAA;;;;"}