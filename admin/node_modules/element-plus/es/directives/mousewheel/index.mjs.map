{"version": 3, "file": "index.mjs", "sources": ["../../../../../packages/directives/mousewheel/index.ts"], "sourcesContent": ["import normalizeWheel from 'normalize-wheel-es'\n\nimport type { DirectiveBinding, ObjectDirective } from 'vue'\nimport type { NormalizedWheelEvent } from 'normalize-wheel-es'\n\nconst mousewheel = function (\n  element: HTMLElement,\n  callback: (e: WheelEvent, normalized: NormalizedWheelEvent) => void\n) {\n  if (element && element.addEventListener) {\n    const fn = function (this: HTMLElement, event: WheelEvent) {\n      const normalized = normalizeWheel(event)\n      callback && Reflect.apply(callback, this, [event, normalized])\n    }\n    element.addEventListener('wheel', fn, { passive: true })\n  }\n}\n\nconst Mousewheel: ObjectDirective = {\n  beforeMount(el: HTMLElement, binding: DirectiveBinding) {\n    mousewheel(el, binding.value)\n  },\n}\n\nexport default Mousewheel\n"], "names": [], "mappings": ";;AACA,MAAM,UAAU,GAAG,SAAS,OAAO,EAAE,QAAQ,EAAE;AAC/C,EAAE,IAAI,OAAO,IAAI,OAAO,CAAC,gBAAgB,EAAE;AAC3C,IAAI,MAAM,EAAE,GAAG,SAAS,KAAK,EAAE;AAC/B,MAAM,MAAM,UAAU,GAAG,cAAc,CAAC,KAAK,CAAC,CAAC;AAC/C,MAAM,QAAQ,IAAI,OAAO,CAAC,KAAK,CAAC,QAAQ,EAAE,IAAI,EAAE,CAAC,KAAK,EAAE,UAAU,CAAC,CAAC,CAAC;AACrE,KAAK,CAAC;AACN,IAAI,OAAO,CAAC,gBAAgB,CAAC,OAAO,EAAE,EAAE,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC,CAAC;AAC7D,GAAG;AACH,CAAC,CAAC;AACG,MAAC,UAAU,GAAG;AACnB,EAAE,WAAW,CAAC,EAAE,EAAE,OAAO,EAAE;AAC3B,IAAI,UAAU,CAAC,EAAE,EAAE,OAAO,CAAC,KAAK,CAAC,CAAC;AAClC,GAAG;AACH;;;;"}