{"version": 3, "file": "index.mjs", "sources": ["../../../../../packages/hooks/use-ordered-children/index.ts"], "sourcesContent": ["import { isVNode, shallowRef } from 'vue'\nimport { flattedChildren } from '@element-plus/utils'\n\nimport type { ComponentInternalInstance, VNode } from 'vue'\n\nconst getOrderedChildren = <T>(\n  vm: ComponentInternalInstance,\n  childComponentName: string,\n  children: Record<number, T>\n): T[] => {\n  const nodes = flattedChildren(vm.subTree).filter(\n    (n): n is VNode =>\n      isVNode(n) &&\n      (n.type as any)?.name === childComponentName &&\n      !!n.component\n  )\n  const uids = nodes.map((n) => n.component!.uid)\n  return uids.map((uid) => children[uid]).filter((p) => !!p)\n}\n\nexport const useOrderedChildren = <T extends { uid: number }>(\n  vm: ComponentInternalInstance,\n  childComponentName: string\n) => {\n  const children: Record<number, T> = {}\n  const orderedChildren = shallowRef<T[]>([])\n\n  // TODO: split into two functions: addChild and sortChildren\n  const addChild = (child: T) => {\n    children[child.uid] = child\n    orderedChildren.value = getOrderedChildren(vm, childComponentName, children)\n  }\n  const removeChild = (uid: number) => {\n    delete children[uid]\n    orderedChildren.value = orderedChildren.value.filter(\n      (children) => children.uid !== uid\n    )\n  }\n\n  return {\n    children: orderedChildren,\n    addChild,\n    removeChild,\n  }\n}\n"], "names": [], "mappings": ";;;AAEA,MAAM,kBAAkB,GAAG,CAAC,EAAE,EAAE,kBAAkB,EAAE,QAAQ,KAAK;AACjE,EAAE,MAAM,KAAK,GAAG,eAAe,CAAC,EAAE,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK;AAC1D,IAAI,IAAI,EAAE,CAAC;AACX,IAAI,OAAO,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,IAAI,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,IAAI,MAAM,kBAAkB,IAAI,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;AAC5G,GAAG,CAAC,CAAC;AACL,EAAE,MAAM,IAAI,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;AACjD,EAAE,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,KAAK,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;AAC7D,CAAC,CAAC;AACU,MAAC,kBAAkB,GAAG,CAAC,EAAE,EAAE,kBAAkB,KAAK;AAC9D,EAAE,MAAM,QAAQ,GAAG,EAAE,CAAC;AACtB,EAAE,MAAM,eAAe,GAAG,UAAU,CAAC,EAAE,CAAC,CAAC;AACzC,EAAE,MAAM,QAAQ,GAAG,CAAC,KAAK,KAAK;AAC9B,IAAI,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;AAChC,IAAI,eAAe,CAAC,KAAK,GAAG,kBAAkB,CAAC,EAAE,EAAE,kBAAkB,EAAE,QAAQ,CAAC,CAAC;AACjF,GAAG,CAAC;AACJ,EAAE,MAAM,WAAW,GAAG,CAAC,GAAG,KAAK;AAC/B,IAAI,OAAO,QAAQ,CAAC,GAAG,CAAC,CAAC;AACzB,IAAI,eAAe,CAAC,KAAK,GAAG,eAAe,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,SAAS,KAAK,SAAS,CAAC,GAAG,KAAK,GAAG,CAAC,CAAC;AAC/F,GAAG,CAAC;AACJ,EAAE,OAAO;AACT,IAAI,QAAQ,EAAE,eAAe;AAC7B,IAAI,QAAQ;AACZ,IAAI,WAAW;AACf,GAAG,CAAC;AACJ;;;;"}