{"version": 3, "file": "index.mjs", "sources": ["../../../../../packages/hooks/use-size/index.ts"], "sourcesContent": ["import { computed, inject, unref } from 'vue'\nimport { buildProp } from '@element-plus/utils'\nimport { componentSizes } from '@element-plus/constants'\n\nimport type { InjectionKey, Ref } from 'vue'\nimport type { ComponentSize } from '@element-plus/constants'\n\nexport const useSizeProp = buildProp({\n  type: String,\n  values: componentSizes,\n  required: false,\n} as const)\n\nexport const useSizeProps = {\n  size: useSizeProp,\n}\n\nexport interface SizeContext {\n  size: Ref<ComponentSize>\n}\n\nexport const SIZE_INJECTION_KEY: InjectionKey<SizeContext> = Symbol('size')\n\nexport const useGlobalSize = () => {\n  const injectedSize = inject(SIZE_INJECTION_KEY, {} as SizeContext)\n\n  return computed<ComponentSize>(() => {\n    return unref(injectedSize.size) || ''\n  })\n}\n"], "names": [], "mappings": ";;;;AAGY,MAAC,WAAW,GAAG,SAAS,CAAC;AACrC,EAAE,IAAI,EAAE,MAAM;AACd,EAAE,MAAM,EAAE,cAAc;AACxB,EAAE,QAAQ,EAAE,KAAK;AACjB,CAAC,EAAE;AACS,MAAC,YAAY,GAAG;AAC5B,EAAE,IAAI,EAAE,WAAW;AACnB,EAAE;AACU,MAAC,kBAAkB,GAAG,MAAM,CAAC,MAAM,EAAE;AACrC,MAAC,aAAa,GAAG,MAAM;AACnC,EAAE,MAAM,YAAY,GAAG,MAAM,CAAC,kBAAkB,EAAE,EAAE,CAAC,CAAC;AACtD,EAAE,OAAO,QAAQ,CAAC,MAAM;AACxB,IAAI,OAAO,KAAK,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;AAC1C,GAAG,CAAC,CAAC;AACL;;;;"}