{"version": 3, "file": "objects.mjs", "sources": ["../../../../packages/utils/objects.ts"], "sourcesContent": ["import { get, set } from 'lodash-unified'\nimport type { Entries } from 'type-fest'\nimport type { Arrayable } from '.'\n\nexport const keysOf = <T extends object>(arr: T) =>\n  Object.keys(arr) as Array<keyof T>\nexport const entriesOf = <T extends object>(arr: T) =>\n  Object.entries(arr) as Entries<T>\nexport { hasOwn } from '@vue/shared'\n\nexport const getProp = <T = any>(\n  obj: Record<string, any>,\n  path: Arrayable<string>,\n  defaultValue?: any\n): { value: T } => {\n  return {\n    get value() {\n      return get(obj, path, defaultValue)\n    },\n    set value(val: any) {\n      set(obj, path, val)\n    },\n  }\n}\n"], "names": [], "mappings": ";;;AACY,MAAC,MAAM,GAAG,CAAC,GAAG,KAAK,MAAM,CAAC,IAAI,CAAC,GAAG,EAAE;AACpC,MAAC,SAAS,GAAG,CAAC,GAAG,KAAK,MAAM,CAAC,OAAO,CAAC,GAAG,EAAE;AAE1C,MAAC,OAAO,GAAG,CAAC,GAAG,EAAE,IAAI,EAAE,YAAY,KAAK;AACpD,EAAE,OAAO;AACT,IAAI,IAAI,KAAK,GAAG;AAChB,MAAM,OAAO,GAAG,CAAC,GAAG,EAAE,IAAI,EAAE,YAAY,CAAC,CAAC;AAC1C,KAAK;AACL,IAAI,IAAI,KAAK,CAAC,GAAG,EAAE;AACnB,MAAM,GAAG,CAAC,GAAG,EAAE,IAAI,EAAE,GAAG,CAAC,CAAC;AAC1B,KAAK;AACL,GAAG,CAAC;AACJ;;;;"}