{"version": 3, "file": "install.mjs", "sources": ["../../../../../packages/utils/vue/install.ts"], "sourcesContent": ["import { NOOP } from '../functions'\n\nimport type { App, Directive } from 'vue'\nimport type { SFCInstallWithContext, SFCWithInstall } from './typescript'\n\nexport const withInstall = <T, E extends Record<string, any>>(\n  main: T,\n  extra?: E\n) => {\n  ;(main as SFCWithInstall<T>).install = (app): void => {\n    for (const comp of [main, ...Object.values(extra ?? {})]) {\n      app.component(comp.name, comp)\n    }\n  }\n\n  if (extra) {\n    for (const [key, comp] of Object.entries(extra)) {\n      ;(main as any)[key] = comp\n    }\n  }\n  return main as SFCWithInstall<T> & E\n}\n\nexport const withInstallFunction = <T>(fn: T, name: string) => {\n  ;(fn as SFCWithInstall<T>).install = (app: App) => {\n    ;(fn as SFCInstallWithContext<T>)._context = app._context\n    app.config.globalProperties[name] = fn\n  }\n\n  return fn as SFCInstallWithContext<T>\n}\n\nexport const withInstallDirective = <T extends Directive>(\n  directive: T,\n  name: string\n) => {\n  ;(directive as SFCWithInstall<T>).install = (app: App): void => {\n    app.directive(name, directive)\n  }\n\n  return directive as SFCWithInstall<T>\n}\n\nexport const withNoopInstall = <T>(component: T) => {\n  ;(component as SFCWithInstall<T>).install = NOOP\n\n  return component as SFCWithInstall<T>\n}\n"], "names": [], "mappings": ";;AACY,MAAC,WAAW,GAAG,CAAC,IAAI,EAAE,KAAK,KAAK;AAE5C,EAAE,IAAI,CAAC,OAAO,GAAG,CAAC,GAAG,KAAK;AAC1B,IAAI,KAAK,MAAM,IAAI,IAAI,CAAC,IAAI,EAAE,GAAG,MAAM,CAAC,MAAM,CAAC,KAAK,IAAI,IAAI,GAAG,KAAK,GAAG,EAAE,CAAC,CAAC,EAAE;AAC7E,MAAM,GAAG,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;AACrC,KAAK;AACL,GAAG,CAAC;AACJ,EAAE,IAAI,KAAK,EAAE;AACb,IAAI,KAAK,MAAM,CAAC,GAAG,EAAE,IAAI,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;AAErD,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC;AACvB,KAAK;AACL,GAAG;AACH,EAAE,OAAO,IAAI,CAAC;AACd,EAAE;AACU,MAAC,mBAAmB,GAAG,CAAC,EAAE,EAAE,IAAI,KAAK;AAEjD,EAAE,EAAE,CAAC,OAAO,GAAG,CAAC,GAAG,KAAK;AAExB,IAAI,EAAE,CAAC,QAAQ,GAAG,GAAG,CAAC,QAAQ,CAAC;AAC/B,IAAI,GAAG,CAAC,MAAM,CAAC,gBAAgB,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC;AAC3C,GAAG,CAAC;AACJ,EAAE,OAAO,EAAE,CAAC;AACZ,EAAE;AACU,MAAC,oBAAoB,GAAG,CAAC,SAAS,EAAE,IAAI,KAAK;AAEzD,EAAE,SAAS,CAAC,OAAO,GAAG,CAAC,GAAG,KAAK;AAC/B,IAAI,GAAG,CAAC,SAAS,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;AACnC,GAAG,CAAC;AACJ,EAAE,OAAO,SAAS,CAAC;AACnB,EAAE;AACU,MAAC,eAAe,GAAG,CAAC,SAAS,KAAK;AAE9C,EAAE,SAAS,CAAC,OAAO,GAAG,IAAI,CAAC;AAC3B,EAAE,OAAO,SAAS,CAAC;AACnB;;;;"}