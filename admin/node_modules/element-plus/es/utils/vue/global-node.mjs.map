{"version": 3, "file": "global-node.mjs", "sources": ["../../../../../packages/utils/vue/global-node.ts"], "sourcesContent": ["import { isClient } from '../browser'\n\nconst globalNodes: HTMLElement[] = []\nlet target: HTMLElement | undefined = !isClient ? undefined : document.body\n\nexport function createGlobalNode(id?: string) {\n  const el = document.createElement('div')\n  if (id !== undefined) {\n    el.setAttribute('id', id)\n  }\n\n  if (target) {\n    target.appendChild(el)\n    globalNodes.push(el)\n  }\n\n  return el\n}\n\nexport function removeGlobalNode(el: HTMLElement) {\n  globalNodes.splice(globalNodes.indexOf(el), 1)\n  el.remove()\n}\n\nexport function changeGlobalNodesTarget(el: HTMLElement) {\n  if (el === target) return\n\n  target = el\n  globalNodes.forEach((el) => {\n    if (target && !el.contains(target)) {\n      target.appendChild(el)\n    }\n  })\n}\n"], "names": [], "mappings": ";;AACA,MAAM,WAAW,GAAG,EAAE,CAAC;AACvB,IAAI,MAAM,GAAG,CAAC,QAAQ,GAAG,KAAK,CAAC,GAAG,QAAQ,CAAC,IAAI,CAAC;AACzC,SAAS,gBAAgB,CAAC,EAAE,EAAE;AACrC,EAAE,MAAM,EAAE,GAAG,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;AAC3C,EAAE,IAAI,EAAE,KAAK,KAAK,CAAC,EAAE;AACrB,IAAI,EAAE,CAAC,YAAY,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;AAC9B,GAAG;AACH,EAAE,IAAI,MAAM,EAAE;AACd,IAAI,MAAM,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC;AAC3B,IAAI,WAAW,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;AACzB,GAAG;AACH,EAAE,OAAO,EAAE,CAAC;AACZ,CAAC;AACM,SAAS,gBAAgB,CAAC,EAAE,EAAE;AACrC,EAAE,WAAW,CAAC,MAAM,CAAC,WAAW,CAAC,OAAO,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;AACjD,EAAE,EAAE,CAAC,MAAM,EAAE,CAAC;AACd,CAAC;AACM,SAAS,uBAAuB,CAAC,EAAE,EAAE;AAC5C,EAAE,IAAI,EAAE,KAAK,MAAM;AACnB,IAAI,OAAO;AACX,EAAE,MAAM,GAAG,EAAE,CAAC;AACd,EAAE,WAAW,CAAC,OAAO,CAAC,CAAC,GAAG,KAAK;AAC/B,IAAI,IAAI,MAAM,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE;AACzC,MAAM,MAAM,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;AAC9B,KAAK;AACL,GAAG,CAAC,CAAC;AACL;;;;"}