{"version": 3, "file": "nb-no.mjs", "sources": ["../../../../../packages/locale/lang/nb-no.ts"], "sourcesContent": ["export default {\n  name: 'nb-no',\n  el: {\n    breadcrumb: {\n      label: 'Breadcrumb', // to be translated\n    },\n    colorpicker: {\n      confirm: 'OK',\n      clear: 'Tøm',\n    },\n    datepicker: {\n      now: 'N<PERSON>',\n      today: 'I dag',\n      cancel: 'Av<PERSON><PERSON><PERSON>',\n      clear: 'Tøm',\n      confirm: 'OK',\n      selectDate: 'Velg dato',\n      selectTime: 'Velg tidspunkt',\n      startDate: 'Startdato',\n      startTime: 'Starttidspunkt',\n      endDate: 'Sluttdato',\n      endTime: 'Sluttidspunkt',\n      prevYear: 'I fjor',\n      nextYear: 'Neste år',\n      prevMonth: '<PERSON><PERSON>e <PERSON>å<PERSON>',\n      nextMonth: 'Neste Måned',\n      year: '',\n      month1: 'Januar',\n      month2: 'Februar',\n      month3: 'Mars',\n      month4: 'April',\n      month5: 'Mai',\n      month6: 'Juni',\n      month7: 'Juli',\n      month8: 'August',\n      month9: 'September',\n      month10: 'Oktober',\n      month11: 'November',\n      month12: 'Des<PERSON>ber',\n      week: 'uke',\n      weeks: {\n        sun: 'Søn',\n        mon: 'Man',\n        tue: 'Tir',\n        wed: 'Ons',\n        thu: 'Tor',\n        fri: 'Fre',\n        sat: 'Lør',\n      },\n      months: {\n        jan: 'Jan',\n        feb: 'Feb',\n        mar: 'Mar',\n        apr: 'Apr',\n        may: 'Mai',\n        jun: 'Jun',\n        jul: 'Jul',\n        aug: 'Aug',\n        sep: 'Sep',\n        oct: 'Okt',\n        nov: 'Nov',\n        dec: 'Des',\n      },\n    },\n    select: {\n      loading: 'Laster',\n      noMatch: 'Ingen samsvarende resulater',\n      noData: 'Ingen resulater',\n      placeholder: 'Velg',\n    },\n    mention: {\n      loading: 'Laster',\n    },\n    cascader: {\n      noMatch: 'Ingen samsvarende resultater',\n      loading: 'Laster',\n      placeholder: 'Velg',\n      noData: 'Ingen resultater',\n    },\n    pagination: {\n      goto: 'Gå til',\n      pagesize: '/side',\n      total: 'Total {total}',\n      pageClassifier: '',\n      page: 'Page', // to be translated\n      prev: 'Go to previous page', // to be translated\n      next: 'Go to next page', // to be translated\n      currentPage: 'page {pager}', // to be translated\n      prevPages: 'Previous {pager} pages', // to be translated\n      nextPages: 'Next {pager} pages', // to be translated\n    },\n    messagebox: {\n      confirm: 'OK',\n      cancel: 'Avbryt',\n      error: 'Ugyldig input',\n    },\n    upload: {\n      deleteTip: 'trykk på x for å slette',\n      delete: 'Slett',\n      preview: 'Forhåndsvisning',\n      continue: 'Fortsett',\n    },\n    table: {\n      emptyText: 'Ingen Data',\n      confirmFilter: 'Bekreft',\n      resetFilter: 'Tilbakestill',\n      clearFilter: 'Alle',\n      sumText: 'Sum',\n    },\n    tree: {\n      emptyText: 'Ingen Data',\n    },\n    transfer: {\n      noMatch: 'Ingen samsvarende data',\n      noData: 'Ingen data',\n      titles: ['Liste 1', 'Liste 2'],\n      filterPlaceholder: 'Skriv inn nøkkelord',\n      noCheckedFormat: '{total} gjenstander',\n      hasCheckedFormat: '{checked}/{total} valgt',\n    },\n    image: {\n      error: 'FEILET',\n    },\n    pageHeader: {\n      title: 'Tilbake',\n    },\n    popconfirm: {\n      confirmButtonText: 'Ja',\n      cancelButtonText: 'Nei',\n    },\n    carousel: {\n      leftArrow: 'Carousel arrow left', // to be translated\n      rightArrow: 'Carousel arrow right', // to be translated\n      indicator: 'Carousel switch to index {index}', // to be translated\n    },\n  },\n}\n"], "names": [], "mappings": "AAAA,WAAe;AACf,EAAE,IAAI,EAAE,OAAO;AACf,EAAE,EAAE,EAAE;AACN,IAAI,UAAU,EAAE;AAChB,MAAM,KAAK,EAAE,YAAY;AACzB,KAAK;AACL,IAAI,WAAW,EAAE;AACjB,MAAM,OAAO,EAAE,IAAI;AACnB,MAAM,KAAK,EAAE,QAAQ;AACrB,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,GAAG,EAAE,OAAO;AAClB,MAAM,KAAK,EAAE,OAAO;AACpB,MAAM,MAAM,EAAE,QAAQ;AACtB,MAAM,KAAK,EAAE,QAAQ;AACrB,MAAM,OAAO,EAAE,IAAI;AACnB,MAAM,UAAU,EAAE,WAAW;AAC7B,MAAM,UAAU,EAAE,gBAAgB;AAClC,MAAM,SAAS,EAAE,WAAW;AAC5B,MAAM,SAAS,EAAE,gBAAgB;AACjC,MAAM,OAAO,EAAE,WAAW;AAC1B,MAAM,OAAO,EAAE,eAAe;AAC9B,MAAM,QAAQ,EAAE,QAAQ;AACxB,MAAM,QAAQ,EAAE,aAAa;AAC7B,MAAM,SAAS,EAAE,kBAAkB;AACnC,MAAM,SAAS,EAAE,gBAAgB;AACjC,MAAM,IAAI,EAAE,EAAE;AACd,MAAM,MAAM,EAAE,QAAQ;AACtB,MAAM,MAAM,EAAE,SAAS;AACvB,MAAM,MAAM,EAAE,MAAM;AACpB,MAAM,MAAM,EAAE,OAAO;AACrB,MAAM,MAAM,EAAE,KAAK;AACnB,MAAM,MAAM,EAAE,MAAM;AACpB,MAAM,MAAM,EAAE,MAAM;AACpB,MAAM,MAAM,EAAE,QAAQ;AACtB,MAAM,MAAM,EAAE,WAAW;AACzB,MAAM,OAAO,EAAE,SAAS;AACxB,MAAM,OAAO,EAAE,UAAU;AACzB,MAAM,OAAO,EAAE,UAAU;AACzB,MAAM,IAAI,EAAE,KAAK;AACjB,MAAM,KAAK,EAAE;AACb,QAAQ,GAAG,EAAE,QAAQ;AACrB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,QAAQ;AACrB,OAAO;AACP,MAAM,MAAM,EAAE;AACd,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,OAAO;AACP,KAAK;AACL,IAAI,MAAM,EAAE;AACZ,MAAM,OAAO,EAAE,QAAQ;AACvB,MAAM,OAAO,EAAE,6BAA6B;AAC5C,MAAM,MAAM,EAAE,iBAAiB;AAC/B,MAAM,WAAW,EAAE,MAAM;AACzB,KAAK;AACL,IAAI,OAAO,EAAE;AACb,MAAM,OAAO,EAAE,QAAQ;AACvB,KAAK;AACL,IAAI,QAAQ,EAAE;AACd,MAAM,OAAO,EAAE,8BAA8B;AAC7C,MAAM,OAAO,EAAE,QAAQ;AACvB,MAAM,WAAW,EAAE,MAAM;AACzB,MAAM,MAAM,EAAE,kBAAkB;AAChC,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,IAAI,EAAE,WAAW;AACvB,MAAM,QAAQ,EAAE,OAAO;AACvB,MAAM,KAAK,EAAE,eAAe;AAC5B,MAAM,cAAc,EAAE,EAAE;AACxB,MAAM,IAAI,EAAE,MAAM;AAClB,MAAM,IAAI,EAAE,qBAAqB;AACjC,MAAM,IAAI,EAAE,iBAAiB;AAC7B,MAAM,WAAW,EAAE,cAAc;AACjC,MAAM,SAAS,EAAE,wBAAwB;AACzC,MAAM,SAAS,EAAE,oBAAoB;AACrC,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,OAAO,EAAE,IAAI;AACnB,MAAM,MAAM,EAAE,QAAQ;AACtB,MAAM,KAAK,EAAE,eAAe;AAC5B,KAAK;AACL,IAAI,MAAM,EAAE;AACZ,MAAM,SAAS,EAAE,+BAA+B;AAChD,MAAM,MAAM,EAAE,OAAO;AACrB,MAAM,OAAO,EAAE,oBAAoB;AACnC,MAAM,QAAQ,EAAE,UAAU;AAC1B,KAAK;AACL,IAAI,KAAK,EAAE;AACX,MAAM,SAAS,EAAE,YAAY;AAC7B,MAAM,aAAa,EAAE,SAAS;AAC9B,MAAM,WAAW,EAAE,cAAc;AACjC,MAAM,WAAW,EAAE,MAAM;AACzB,MAAM,OAAO,EAAE,KAAK;AACpB,KAAK;AACL,IAAI,IAAI,EAAE;AACV,MAAM,SAAS,EAAE,YAAY;AAC7B,KAAK;AACL,IAAI,QAAQ,EAAE;AACd,MAAM,OAAO,EAAE,wBAAwB;AACvC,MAAM,MAAM,EAAE,YAAY;AAC1B,MAAM,MAAM,EAAE,CAAC,SAAS,EAAE,SAAS,CAAC;AACpC,MAAM,iBAAiB,EAAE,wBAAwB;AACjD,MAAM,eAAe,EAAE,qBAAqB;AAC5C,MAAM,gBAAgB,EAAE,yBAAyB;AACjD,KAAK;AACL,IAAI,KAAK,EAAE;AACX,MAAM,KAAK,EAAE,QAAQ;AACrB,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,KAAK,EAAE,SAAS;AACtB,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,iBAAiB,EAAE,IAAI;AAC7B,MAAM,gBAAgB,EAAE,KAAK;AAC7B,KAAK;AACL,IAAI,QAAQ,EAAE;AACd,MAAM,SAAS,EAAE,qBAAqB;AACtC,MAAM,UAAU,EAAE,sBAAsB;AACxC,MAAM,SAAS,EAAE,kCAAkC;AACnD,KAAK;AACL,GAAG;AACH,CAAC;;;;"}