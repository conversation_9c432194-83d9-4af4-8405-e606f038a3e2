{"version": 3, "file": "en.mjs", "sources": ["../../../../../packages/locale/lang/en.ts"], "sourcesContent": ["export default {\n  name: 'en',\n  el: {\n    breadcrumb: {\n      label: 'Breadcrumb', // to be translated\n    },\n    colorpicker: {\n      confirm: 'OK',\n      clear: 'Clear',\n      defaultLabel: 'color picker',\n      description:\n        'current color is {color}. press enter to select a new color.',\n      alphaLabel: 'pick alpha value',\n    },\n    datepicker: {\n      now: 'Now',\n      today: 'Today',\n      cancel: 'Cancel',\n      clear: 'Clear',\n      confirm: 'OK',\n      dateTablePrompt:\n        'Use the arrow keys and enter to select the day of the month',\n      monthTablePrompt: 'Use the arrow keys and enter to select the month',\n      yearTablePrompt: 'Use the arrow keys and enter to select the year',\n      selectedDate: 'Selected date',\n      selectDate: 'Select date',\n      selectTime: 'Select time',\n      startDate: 'Start Date',\n      startTime: 'Start Time',\n      endDate: 'End Date',\n      endTime: 'End Time',\n      prevYear: 'Previous Year',\n      nextYear: 'Next Year',\n      prevMonth: 'Previous Month',\n      nextMonth: 'Next Month',\n      year: '',\n      month1: 'January',\n      month2: 'February',\n      month3: 'March',\n      month4: 'April',\n      month5: 'May',\n      month6: 'June',\n      month7: 'July',\n      month8: 'August',\n      month9: 'September',\n      month10: 'October',\n      month11: 'November',\n      month12: 'December',\n      week: 'week',\n      weeks: {\n        sun: 'Sun',\n        mon: 'Mon',\n        tue: 'Tue',\n        wed: 'Wed',\n        thu: 'Thu',\n        fri: 'Fri',\n        sat: 'Sat',\n      },\n      weeksFull: {\n        sun: 'Sunday',\n        mon: 'Monday',\n        tue: 'Tuesday',\n        wed: 'Wednesday',\n        thu: 'Thursday',\n        fri: 'Friday',\n        sat: 'Saturday',\n      },\n      months: {\n        jan: 'Jan',\n        feb: 'Feb',\n        mar: 'Mar',\n        apr: 'Apr',\n        may: 'May',\n        jun: 'Jun',\n        jul: 'Jul',\n        aug: 'Aug',\n        sep: 'Sep',\n        oct: 'Oct',\n        nov: 'Nov',\n        dec: 'Dec',\n      },\n    },\n    inputNumber: {\n      decrease: 'decrease number',\n      increase: 'increase number',\n    },\n    select: {\n      loading: 'Loading',\n      noMatch: 'No matching data',\n      noData: 'No data',\n      placeholder: 'Select',\n    },\n    mention: {\n      loading: 'Loading',\n    },\n    dropdown: {\n      toggleDropdown: 'Toggle Dropdown',\n    },\n    cascader: {\n      noMatch: 'No matching data',\n      loading: 'Loading',\n      placeholder: 'Select',\n      noData: 'No data',\n    },\n    pagination: {\n      goto: 'Go to',\n      pagesize: '/page',\n      total: 'Total {total}',\n      pageClassifier: '',\n      page: 'Page',\n      prev: 'Go to previous page',\n      next: 'Go to next page',\n      currentPage: 'page {pager}',\n      prevPages: 'Previous {pager} pages',\n      nextPages: 'Next {pager} pages',\n      deprecationWarning:\n        'Deprecated usages detected, please refer to the el-pagination documentation for more details',\n    },\n    dialog: {\n      close: 'Close this dialog',\n    },\n    drawer: {\n      close: 'Close this dialog',\n    },\n    messagebox: {\n      title: 'Message',\n      confirm: 'OK',\n      cancel: 'Cancel',\n      error: 'Illegal input',\n      close: 'Close this dialog',\n    },\n    upload: {\n      deleteTip: 'press delete to remove',\n      delete: 'Delete',\n      preview: 'Preview',\n      continue: 'Continue',\n    },\n    slider: {\n      defaultLabel: 'slider between {min} and {max}',\n      defaultRangeStartLabel: 'pick start value',\n      defaultRangeEndLabel: 'pick end value',\n    },\n    table: {\n      emptyText: 'No Data',\n      confirmFilter: 'Confirm',\n      resetFilter: 'Reset',\n      clearFilter: 'All',\n      sumText: 'Sum',\n    },\n    tour: {\n      next: 'Next',\n      previous: 'Previous',\n      finish: 'Finish',\n    },\n    tree: {\n      emptyText: 'No Data',\n    },\n    transfer: {\n      noMatch: 'No matching data',\n      noData: 'No data',\n      titles: ['List 1', 'List 2'], // to be translated\n      filterPlaceholder: 'Enter keyword', // to be translated\n      noCheckedFormat: '{total} items', // to be translated\n      hasCheckedFormat: '{checked}/{total} checked', // to be translated\n    },\n    image: {\n      error: 'FAILED',\n    },\n    pageHeader: {\n      title: 'Back', // to be translated\n    },\n    popconfirm: {\n      confirmButtonText: 'Yes',\n      cancelButtonText: 'No',\n    },\n    carousel: {\n      leftArrow: 'Carousel arrow left',\n      rightArrow: 'Carousel arrow right',\n      indicator: 'Carousel switch to index {index}',\n    },\n  },\n}\n"], "names": [], "mappings": "AAAA,cAAe;AACf,EAAE,IAAI,EAAE,IAAI;AACZ,EAAE,EAAE,EAAE;AACN,IAAI,UAAU,EAAE;AAChB,MAAM,KAAK,EAAE,YAAY;AACzB,KAAK;AACL,IAAI,WAAW,EAAE;AACjB,MAAM,OAAO,EAAE,IAAI;AACnB,MAAM,KAAK,EAAE,OAAO;AACpB,MAAM,YAAY,EAAE,cAAc;AAClC,MAAM,WAAW,EAAE,8DAA8D;AACjF,MAAM,UAAU,EAAE,kBAAkB;AACpC,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,GAAG,EAAE,KAAK;AAChB,MAAM,KAAK,EAAE,OAAO;AACpB,MAAM,MAAM,EAAE,QAAQ;AACtB,MAAM,KAAK,EAAE,OAAO;AACpB,MAAM,OAAO,EAAE,IAAI;AACnB,MAAM,eAAe,EAAE,6DAA6D;AACpF,MAAM,gBAAgB,EAAE,kDAAkD;AAC1E,MAAM,eAAe,EAAE,iDAAiD;AACxE,MAAM,YAAY,EAAE,eAAe;AACnC,MAAM,UAAU,EAAE,aAAa;AAC/B,MAAM,UAAU,EAAE,aAAa;AAC/B,MAAM,SAAS,EAAE,YAAY;AAC7B,MAAM,SAAS,EAAE,YAAY;AAC7B,MAAM,OAAO,EAAE,UAAU;AACzB,MAAM,OAAO,EAAE,UAAU;AACzB,MAAM,QAAQ,EAAE,eAAe;AAC/B,MAAM,QAAQ,EAAE,WAAW;AAC3B,MAAM,SAAS,EAAE,gBAAgB;AACjC,MAAM,SAAS,EAAE,YAAY;AAC7B,MAAM,IAAI,EAAE,EAAE;AACd,MAAM,MAAM,EAAE,SAAS;AACvB,MAAM,MAAM,EAAE,UAAU;AACxB,MAAM,MAAM,EAAE,OAAO;AACrB,MAAM,MAAM,EAAE,OAAO;AACrB,MAAM,MAAM,EAAE,KAAK;AACnB,MAAM,MAAM,EAAE,MAAM;AACpB,MAAM,MAAM,EAAE,MAAM;AACpB,MAAM,MAAM,EAAE,QAAQ;AACtB,MAAM,MAAM,EAAE,WAAW;AACzB,MAAM,OAAO,EAAE,SAAS;AACxB,MAAM,OAAO,EAAE,UAAU;AACzB,MAAM,OAAO,EAAE,UAAU;AACzB,MAAM,IAAI,EAAE,MAAM;AAClB,MAAM,KAAK,EAAE;AACb,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,OAAO;AACP,MAAM,SAAS,EAAE;AACjB,QAAQ,GAAG,EAAE,QAAQ;AACrB,QAAQ,GAAG,EAAE,QAAQ;AACrB,QAAQ,GAAG,EAAE,SAAS;AACtB,QAAQ,GAAG,EAAE,WAAW;AACxB,QAAQ,GAAG,EAAE,UAAU;AACvB,QAAQ,GAAG,EAAE,QAAQ;AACrB,QAAQ,GAAG,EAAE,UAAU;AACvB,OAAO;AACP,MAAM,MAAM,EAAE;AACd,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,OAAO;AACP,KAAK;AACL,IAAI,WAAW,EAAE;AACjB,MAAM,QAAQ,EAAE,iBAAiB;AACjC,MAAM,QAAQ,EAAE,iBAAiB;AACjC,KAAK;AACL,IAAI,MAAM,EAAE;AACZ,MAAM,OAAO,EAAE,SAAS;AACxB,MAAM,OAAO,EAAE,kBAAkB;AACjC,MAAM,MAAM,EAAE,SAAS;AACvB,MAAM,WAAW,EAAE,QAAQ;AAC3B,KAAK;AACL,IAAI,OAAO,EAAE;AACb,MAAM,OAAO,EAAE,SAAS;AACxB,KAAK;AACL,IAAI,QAAQ,EAAE;AACd,MAAM,cAAc,EAAE,iBAAiB;AACvC,KAAK;AACL,IAAI,QAAQ,EAAE;AACd,MAAM,OAAO,EAAE,kBAAkB;AACjC,MAAM,OAAO,EAAE,SAAS;AACxB,MAAM,WAAW,EAAE,QAAQ;AAC3B,MAAM,MAAM,EAAE,SAAS;AACvB,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,IAAI,EAAE,OAAO;AACnB,MAAM,QAAQ,EAAE,OAAO;AACvB,MAAM,KAAK,EAAE,eAAe;AAC5B,MAAM,cAAc,EAAE,EAAE;AACxB,MAAM,IAAI,EAAE,MAAM;AAClB,MAAM,IAAI,EAAE,qBAAqB;AACjC,MAAM,IAAI,EAAE,iBAAiB;AAC7B,MAAM,WAAW,EAAE,cAAc;AACjC,MAAM,SAAS,EAAE,wBAAwB;AACzC,MAAM,SAAS,EAAE,oBAAoB;AACrC,MAAM,kBAAkB,EAAE,8FAA8F;AACxH,KAAK;AACL,IAAI,MAAM,EAAE;AACZ,MAAM,KAAK,EAAE,mBAAmB;AAChC,KAAK;AACL,IAAI,MAAM,EAAE;AACZ,MAAM,KAAK,EAAE,mBAAmB;AAChC,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,KAAK,EAAE,SAAS;AACtB,MAAM,OAAO,EAAE,IAAI;AACnB,MAAM,MAAM,EAAE,QAAQ;AACtB,MAAM,KAAK,EAAE,eAAe;AAC5B,MAAM,KAAK,EAAE,mBAAmB;AAChC,KAAK;AACL,IAAI,MAAM,EAAE;AACZ,MAAM,SAAS,EAAE,wBAAwB;AACzC,MAAM,MAAM,EAAE,QAAQ;AACtB,MAAM,OAAO,EAAE,SAAS;AACxB,MAAM,QAAQ,EAAE,UAAU;AAC1B,KAAK;AACL,IAAI,MAAM,EAAE;AACZ,MAAM,YAAY,EAAE,gCAAgC;AACpD,MAAM,sBAAsB,EAAE,kBAAkB;AAChD,MAAM,oBAAoB,EAAE,gBAAgB;AAC5C,KAAK;AACL,IAAI,KAAK,EAAE;AACX,MAAM,SAAS,EAAE,SAAS;AAC1B,MAAM,aAAa,EAAE,SAAS;AAC9B,MAAM,WAAW,EAAE,OAAO;AAC1B,MAAM,WAAW,EAAE,KAAK;AACxB,MAAM,OAAO,EAAE,KAAK;AACpB,KAAK;AACL,IAAI,IAAI,EAAE;AACV,MAAM,IAAI,EAAE,MAAM;AAClB,MAAM,QAAQ,EAAE,UAAU;AAC1B,MAAM,MAAM,EAAE,QAAQ;AACtB,KAAK;AACL,IAAI,IAAI,EAAE;AACV,MAAM,SAAS,EAAE,SAAS;AAC1B,KAAK;AACL,IAAI,QAAQ,EAAE;AACd,MAAM,OAAO,EAAE,kBAAkB;AACjC,MAAM,MAAM,EAAE,SAAS;AACvB,MAAM,MAAM,EAAE,CAAC,QAAQ,EAAE,QAAQ,CAAC;AAClC,MAAM,iBAAiB,EAAE,eAAe;AACxC,MAAM,eAAe,EAAE,eAAe;AACtC,MAAM,gBAAgB,EAAE,2BAA2B;AACnD,KAAK;AACL,IAAI,KAAK,EAAE;AACX,MAAM,KAAK,EAAE,QAAQ;AACrB,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,KAAK,EAAE,MAAM;AACnB,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,iBAAiB,EAAE,KAAK;AAC9B,MAAM,gBAAgB,EAAE,IAAI;AAC5B,KAAK;AACL,IAAI,QAAQ,EAAE;AACd,MAAM,SAAS,EAAE,qBAAqB;AACtC,MAAM,UAAU,EAAE,sBAAsB;AACxC,MAAM,SAAS,EAAE,kCAAkC;AACnD,KAAK;AACL,GAAG;AACH,CAAC;;;;"}