export { default as af } from './lang/af.mjs';
export { default as arEg } from './lang/ar-eg.mjs';
export { default as ar } from './lang/ar.mjs';
export { default as az } from './lang/az.mjs';
export { default as bg } from './lang/bg.mjs';
export { default as bn } from './lang/bn.mjs';
export { default as ca } from './lang/ca.mjs';
export { default as ckb } from './lang/ckb.mjs';
export { default as cs } from './lang/cs.mjs';
export { default as da } from './lang/da.mjs';
export { default as de } from './lang/de.mjs';
export { default as el } from './lang/el.mjs';
export { default as en } from './lang/en.mjs';
export { default as eo } from './lang/eo.mjs';
export { default as es } from './lang/es.mjs';
export { default as et } from './lang/et.mjs';
export { default as eu } from './lang/eu.mjs';
export { default as fa } from './lang/fa.mjs';
export { default as fi } from './lang/fi.mjs';
export { default as fr } from './lang/fr.mjs';
export { default as he } from './lang/he.mjs';
export { default as hi } from './lang/hi.mjs';
export { default as hr } from './lang/hr.mjs';
export { default as hu } from './lang/hu.mjs';
export { default as hyAm } from './lang/hy-am.mjs';
export { default as id } from './lang/id.mjs';
export { default as it } from './lang/it.mjs';
export { default as ja } from './lang/ja.mjs';
export { default as kk } from './lang/kk.mjs';
export { default as km } from './lang/km.mjs';
export { default as ko } from './lang/ko.mjs';
export { default as ku } from './lang/ku.mjs';
export { default as ky } from './lang/ky.mjs';
export { default as lo } from './lang/lo.mjs';
export { default as lt } from './lang/lt.mjs';
export { default as lv } from './lang/lv.mjs';
export { default as mg } from './lang/mg.mjs';
export { default as mn } from './lang/mn.mjs';
export { default as ms } from './lang/ms.mjs';
export { default as my } from './lang/my.mjs';
export { default as nbNo } from './lang/nb-no.mjs';
export { default as nl } from './lang/nl.mjs';
export { default as no } from './lang/no.mjs';
export { default as pa } from './lang/pa.mjs';
export { default as pl } from './lang/pl.mjs';
export { default as ptBr } from './lang/pt-br.mjs';
export { default as pt } from './lang/pt.mjs';
export { default as ro } from './lang/ro.mjs';
export { default as ru } from './lang/ru.mjs';
export { default as sk } from './lang/sk.mjs';
export { default as sl } from './lang/sl.mjs';
export { default as sr } from './lang/sr.mjs';
export { default as sv } from './lang/sv.mjs';
export { default as sw } from './lang/sw.mjs';
export { default as ta } from './lang/ta.mjs';
export { default as te } from './lang/te.mjs';
export { default as th } from './lang/th.mjs';
export { default as tk } from './lang/tk.mjs';
export { default as tr } from './lang/tr.mjs';
export { default as ugCn } from './lang/ug-cn.mjs';
export { default as uk } from './lang/uk.mjs';
export { default as uzUz } from './lang/uz-uz.mjs';
export { default as vi } from './lang/vi.mjs';
export { default as zhCn } from './lang/zh-cn.mjs';
export { default as zhTw } from './lang/zh-tw.mjs';
export { default as zhHk } from './lang/zh-hk.mjs';
export { default as zhMo } from './lang/zh-mo.mjs';
//# sourceMappingURL=index.mjs.map
