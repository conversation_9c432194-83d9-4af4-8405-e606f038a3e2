{"version": 3, "names": ["_arrayLikeToArray", "require", "_unsupportedIterableToArray", "o", "minLen", "arrayLikeToArray", "name", "Object", "prototype", "toString", "call", "slice", "constructor", "Array", "from", "test"], "sources": ["../../src/helpers/unsupportedIterableToArray.ts"], "sourcesContent": ["/* @minVersion 7.9.0 */\n\nimport arrayLikeToArray from \"./arrayLikeToArray.ts\";\n\ntype NonArrayIterable<V, T extends Iterable<V> = Iterable<V>> =\n  T extends Array<any> ? never : Iterable<V>;\n\nexport default function _unsupportedIterableToArray<T>(\n  o: RelativeIndexable<T> /* string | typedarray */ | ArrayLike<T> | Set<T>,\n  minLen?: number | null,\n): T[];\nexport default function _unsupportedIterableToArray<T, K>(\n  o: Map<K, T>,\n  minLen?: number | null,\n): [K, T][];\n// This is a specific overload added specifically for createForOfIteratorHelpers.ts\nexport default function _unsupportedIterableToArray<T>(\n  o: NonArrayIterable<T>,\n  minLen?: number | null,\n): undefined;\nexport default function _unsupportedIterableToArray(\n  o: any,\n  minLen?: number | null,\n): any[] | undefined {\n  if (!o) return;\n  if (typeof o === \"string\") return arrayLikeToArray<string>(o, minLen);\n  var name = Object.prototype.toString.call(o).slice(8, -1);\n  if (name === \"Object\" && o.constructor) name = o.constructor.name;\n  if (name === \"Map\" || name === \"Set\") return Array.from(o);\n  if (\n    name === \"Arguments\" ||\n    /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(name)\n  ) {\n    return arrayLikeToArray(o, minLen);\n  }\n}\n"], "mappings": ";;;;;;AAEA,IAAAA,iBAAA,GAAAC,OAAA;AAkBe,SAASC,2BAA2BA,CACjDC,CAAM,EACNC,MAAsB,EACH;EACnB,IAAI,CAACD,CAAC,EAAE;EACR,IAAI,OAAOA,CAAC,KAAK,QAAQ,EAAE,OAAO,IAAAE,yBAAgB,EAASF,CAAC,EAAEC,MAAM,CAAC;EACrE,IAAIE,IAAI,GAAGC,MAAM,CAACC,SAAS,CAACC,QAAQ,CAACC,IAAI,CAACP,CAAC,CAAC,CAACQ,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EACzD,IAAIL,IAAI,KAAK,QAAQ,IAAIH,CAAC,CAACS,WAAW,EAAEN,IAAI,GAAGH,CAAC,CAACS,WAAW,CAACN,IAAI;EACjE,IAAIA,IAAI,KAAK,KAAK,IAAIA,IAAI,KAAK,KAAK,EAAE,OAAOO,KAAK,CAACC,IAAI,CAACX,CAAC,CAAC;EAC1D,IACEG,IAAI,KAAK,WAAW,IACpB,0CAA0C,CAACS,IAAI,CAACT,IAAI,CAAC,EACrD;IACA,OAAO,IAAAD,yBAAgB,EAACF,CAAC,EAAEC,MAAM,CAAC;EACpC;AACF", "ignoreList": []}