# Actix Web

## 启动项目

### 本地开发

```bash
SERVER_PORT=8081 cargo run
```
### 生产环境打包
```
APP_ENV=production cargo build --release --target x86_64-unknown-linux-musl
```


### 预发环境打包

```bash
APP_ENV=pre cargo build --release --target x86_64-unknown-linux-musl
```
## CICD
在rust-ci.yml中，github自带的


## TODO
1. 生产环境数据建表
2. 配置要区分环境
3. Nginx配置
4. 域名解析

5. 要写个脚本刷封面，每天把那天封面存在，但不是我的oss域名的收藏夹更新成oss域名

写代码的时候，遵循下面的逻辑：
  . 参考项目已有的项目接口和编码规范还有风格
  . 设计代码逻辑的时候，单个文件不要超过200行，如果超过了，就封装到单独的文件中
  . 设计代码逻辑的时候，必要的时候进行适当的封装函数，确保代码逻辑清晰易读
  . 中文回答，写完后尝试编译
  . 除了我当前提到的业务和相关逻辑，不要修改任何其他不相关的代码
  . 写代码的过程中，不需要向我解释，直接写代码就可以。最后给我总结就行了
  . 如果需要新建表，确保新建的表的字段都是可空的
  . 如果在此次实现逻辑的过程中修改了某个接口，那么需要在对应的handle入口处，注释写清楚请求参数和返回数据的格式
  . 最后，写完代码尝试编译测试环境，确保编译通过

我的需求是：
新增一个AI创建或者更新笔记次数的表：
1. 用户id 外键id
2. 使用次数
4. 创建时间
5. 更新时间

然后新增两个接口：1、新建记录且次数设置为20 2、获取用户的使用次数
创建或者更新笔记之前先校验这个表中有没有创建记录，且次数是否是大于0，如果没有记录说明无权使用AI，联系管理员。如果次数小于等于0，就说明会员到期
同时创建笔记或者更新笔记成功之后，需要给用户的次数-1

1. 如果是创建笔记的task，就去redis里面存一个这个用户的标记，表示他有创建笔记的task
2. 用户再次点击创建笔记，去redis里面查有没有这个标记，有的话就返回不成功。
3. 创建笔记完成后，将这个标记位清除
4. 创建失败也要清除这个标记位

6. 客户端一旦创建成功后，后端应该返回任务的id，就开始每3秒轮询一下任务接口，查看有没有完成。完成之后APP内发送一个推送，失败也要发送一个推送